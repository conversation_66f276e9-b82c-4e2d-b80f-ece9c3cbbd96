"""
ASR Custom Parameters Example

This example demonstrates how to use custom parameters with the ASR transcription API.
Custom parameters allow you to pass additional configuration for each transcription request
without needing to reconfigure the entire plugin instance.
"""

import json
import base64
import numpy as np
import requests
from typing import Dict, Any, Optional


def create_sample_audio() -> str:
    """Create sample audio data (sine wave) and encode as base64"""
    # Generate a 1-second sine wave at 440Hz, 16kHz sample rate
    sample_rate = 16000
    duration = 1.0
    frequency = 440.0
    
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    audio = np.sin(2 * np.pi * frequency * t) * 0.5
    
    # Convert to 16-bit PCM
    audio_int16 = (audio * 32767).astype(np.int16)
    
    # Encode as base64
    return base64.b64encode(audio_int16.tobytes()).decode('utf-8')


def transcribe_with_custom_params(
    plugin_config: Dict[str, Any],
    custom_params: Optional[Dict[str, Any]] = None,
    server_url: str = "http://localhost:8080"
) -> Dict[str, Any]:
    """
    Send transcription request with custom parameters
    
    Args:
        plugin_config: Plugin configuration (used to identify the instance)
        custom_params: Optional custom parameters for this transcription
        server_url: ASR server URL
    
    Returns:
        Transcription response
    """
    audio_data = create_sample_audio()
    
    # Prepare request payload
    payload = {
        "audio": audio_data,
        "plugin_config": plugin_config
    }
    
    # Add custom parameters if provided
    if custom_params:
        payload["custom"] = custom_params
    
    # Send request
    response = requests.post(f"{server_url}/transcribe", json=payload)
    response.raise_for_status()
    
    return response.json()


def main():
    """Example usage of custom parameters"""
    
    # Example plugin configuration
    plugin_config = {
        "model_type": "sense_voice",
        "sense_voice": "./models/sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17/model.onnx",
        "tokens": "./models/sherpa-onnx-sense-voice-zh-en-ja-ko-yue-2024-07-17/tokens.txt",
        "num_threads": 1,
        "use_itn": True
    }
    
    print("=== ASR Custom Parameters Example ===\n")
    
    # Example 1: Basic transcription without custom parameters
    print("1. Basic transcription (no custom parameters):")
    try:
        result = transcribe_with_custom_params(plugin_config)
        print(f"   Text: {result['text']}")
        print(f"   Processing time: {result['processing_time']:.3f}s")
        print(f"   Instance ID: {result['instance_id']}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print()
    
    # Example 2: Transcription with custom sample rate
    print("2. Transcription with custom sample rate:")
    custom_params = {
        "sample_rate": 16000,  # Explicitly set sample rate
        "note": "Custom sample rate example"
    }
    try:
        result = transcribe_with_custom_params(plugin_config, custom_params)
        print(f"   Text: {result['text']}")
        print(f"   Processing time: {result['processing_time']:.3f}s")
        print(f"   Custom params used: {custom_params}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print()
    
    # Example 3: Transcription with multiple custom parameters
    print("3. Transcription with multiple custom parameters:")
    custom_params = {
        "language": "en",  # Language hint (if supported by model)
        "sample_rate": 16000,
        "user_id": "example_user",
        "session_id": "session_123",
        "metadata": {
            "source": "microphone",
            "environment": "quiet"
        }
    }
    try:
        result = transcribe_with_custom_params(plugin_config, custom_params)
        print(f"   Text: {result['text']}")
        print(f"   Processing time: {result['processing_time']:.3f}s")
        print(f"   Custom params used: {json.dumps(custom_params, indent=4)}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print("\n=== Example completed ===")
    print("\nNote: This example assumes:")
    print("- ASR server is running on localhost:8080")
    print("- A plugin instance has been created with the specified configuration")
    print("- The Sherpa-ONNX model files are available in the specified paths")


if __name__ == "__main__":
    main() 