"""
ASR Plugin Server

FastAPI server for the ASR plugin.
This server will be automatically managed by the OLV Launcher.
"""

import os
import sys
from pathlib import Path

# Set environment variables for the generic ASR server
os.environ["ASR_ENGINE_MODULE"] = "your_plugin_name.engine"
os.environ["ASR_ENGINE_CLASS"] = "YourASREngine"

from plugins.asr.asr_server import app

if __name__ == "__main__":
    import uvicorn

    uvicorn.run("your_plugin_name.server:app", host="0.0.0.0", port=8000, reload=False)
