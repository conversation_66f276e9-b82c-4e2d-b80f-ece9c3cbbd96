{"name": "remote_asr_example", "version": "1.0.0", "description": "Example Remote ASR Plugin", "author": "OLV Team", "service_type": "asr", "plugin_type": "remote", "base_url": "http://localhost:8080", "plugin_json_schema": {"type": "object", "title": "Remote ASR Engine Configuration", "properties": {"model_name": {"type": "string", "title": "Model Name", "enum": ["whisper-small", "whisper-medium", "whisper-large"], "default": "whisper-small"}, "language": {"type": "string", "title": "Language", "enum": ["auto", "en", "zh", "ja", "ko"], "default": "auto"}, "temperature": {"type": "number", "title": "Temperature", "minimum": 0.0, "maximum": 1.0, "default": 0.0}}, "required": ["model_name"], "default": {"model_name": "whisper-small", "language": "auto", "temperature": 0.0}}, "plugin_ui_schema": {"ui:title": "Remote ASR Configuration", "ui:description": "Configure remote ASR plugin settings", "model_name": {"ui:widget": "select", "ui:placeholder": "Select model..."}, "language": {"ui:widget": "select", "ui:placeholder": "Select language..."}, "temperature": {"ui:widget": "range", "ui:options": {"step": 0.1}}}}