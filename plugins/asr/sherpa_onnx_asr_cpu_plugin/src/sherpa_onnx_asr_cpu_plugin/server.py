"""
Sherpa-ONNX ASR Plugin Server

Uses the generic ASR server with Sherpa-ONNX engine implementation.
"""

import os
import sys
from pathlib import Path

# Set environment variables for the generic ASR server
os.environ["ASR_ENGINE_MODULE"] = "sherpa_onnx_asr_cpu_plugin.engine"
os.environ["ASR_ENGINE_CLASS"] = "SherpaONNXASREngine"

project_root = Path(__file__).resolve().parents[5]
sys.path.insert(0, str(project_root))

from plugins.asr.asr_server import app

if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "sherpa_onnx_asr_cpu_plugin.server:app", host="0.0.0.0", port=8000, reload=False
    )
