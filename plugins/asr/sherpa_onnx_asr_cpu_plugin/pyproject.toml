[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "sherpa-onnx-asr-cpu-plugin"
version = "1.0.0"
description = "Sherpa-ONNX ASR Plugin for OLV"
authors = [
    {name = "OLV Team"}
]
dependencies = [
    "sherpa-onnx>=1.10.39",
    "onnxruntime>=1.20.1",
    "numpy>=1.26.4,<2",
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "python-multipart>=0.0.6",
    "loguru>=0.7.0",
    "requests>=2.31.0",
    "tqdm>=4.66.0",
    "pydantic>=2.11.5",
    "pydantic-settings>=2.9.1"
]
requires-python = ">=3.10"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"