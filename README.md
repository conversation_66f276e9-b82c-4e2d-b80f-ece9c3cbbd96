# OLV Launcher - Universal Plugin System

OLV Launcher is a powerful, unified plugin management system that supports both local and remote plugins for various AI services including ASR (Automatic Speech Recognition), TTS (Text-to-Speech), and LLM (Large Language Models).

## Features

### 🔌 Universal Plugin Support
- **Local Plugins**: Traditional plugins that run as local processes
- **Remote Plugins**: Connect to external plugin services via HTTP APIs
- **Unified Management**: Single interface to manage all plugin types

### 🌐 Service Types
- **ASR (Automatic Speech Recognition)**: Audio transcription services
- **TTS (Text-to-Speech)**: Text to audio synthesis services
- **LLM (Large Language Models)**: Text generation and processing services

### 🚀 Key Capabilities
- **Dynamic Discovery**: Automatic discovery of local and remote plugins
- **Health Monitoring**: Real-time health checks and status monitoring
- **Instance Management**: Create and manage multiple plugin instances with different configurations
- **Port Management**: Automatic port allocation for local plugins
- **Log Management**: Centralized logging and log viewing
- **Schema Validation**: JSON Schema and UI Schema support for configuration validation

## Quick Start

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd OLV

# Install dependencies
pip install -e .

# Start the launcher
python run_launcher.py
```

### Configuration

Create a `.env` file in the project root:

```env
# Server settings
OLV_LAUNCHER_HOST=127.0.0.1
OLV_LAUNCHER_PORT=7000

# Plugin settings
OLV_LAUNCHER_PLUGINS_DIR=plugins
OLV_LAUNCHER_REMOTE_PLUGINS=["http://localhost:8080", "http://remote-asr.example.com"]

# Timeouts
OLV_LAUNCHER_REMOTE_PLUGIN_TIMEOUT=10
OLV_LAUNCHER_PLUGIN_STARTUP_TIMEOUT=600
```

## Plugin System Architecture

### Local Plugins

Local plugins are traditional plugins that run as separate processes managed by the launcher.

**Directory Structure:**
```
plugins/
├── asr/
│   ├── sherpa_onnx_asr_cpu_plugin/
│   │   ├── plugin.json
│   │   ├── pyproject.toml
│   │   └── src/
│   └── your_custom_asr/
│       ├── plugin.json
│       ├── pyproject.toml
│       └── src/
├── tts/
└── llm/
```

**Local Plugin Configuration (plugin.json):**
```json
{
  "name": "sherpa_onnx_asr_cpu_plugin",
  "version": "1.0.0",
  "description": "Sherpa-ONNX ASR Plugin",
  "author": "OLV Team",
  "service_type": "asr",
  "plugin_type": "local",
  "package_manager": "uv",
  "plugin_json_schema": {
    "type": "object",
    "title": "Plugin Configuration",
    "properties": {
      "model_path": {
        "type": "string",
        "title": "Model Path"
      }
    },
    "required": ["model_path"],
    "default": {
      "model_path": "./models/default.onnx"
    }
  },
  "plugin_ui_schema": {
    "ui:title": "Plugin Configuration",
    "model_path": {
      "ui:widget": "textarea"
    }
  }
}
```

### Remote Plugins

Remote plugins are external services that expose the same API interface as local plugins.

**Remote Plugin Configuration (plugin.json):**
```json
{
  "name": "remote_asr_service",
  "version": "1.0.0",
  "description": "Remote ASR Service",
  "author": "External Provider",
  "service_type": "asr",
  "plugin_type": "remote",
  "base_url": "http://remote-asr.example.com",
  "plugin_json_schema": {
    "type": "object",
    "title": "Remote ASR Configuration",
    "properties": {
      "model_name": {
        "type": "string",
        "enum": ["whisper-small", "whisper-large"]
      }
    }
  }
}
```

## API Reference

### Plugin Management

#### List All Plugins
```http
GET /plugins/
```

#### List Plugins by Service Type
```http
GET /plugins/{service_type}
```

#### Start Plugin
```http
POST /plugins/{plugin_name}/start
```

#### Stop Plugin
```http
POST /plugins/{plugin_name}/stop
```

#### Check Plugin Health
```http
GET /plugins/{plugin_name}/health
```

### Plugin Configuration

#### Get Plugin Schemas
```http
GET /plugins/{plugin_name}/schemas
```

#### Get Plugin JSON Schema
```http
GET /plugins/{plugin_name}/schemas/plugin/json
```

### Instance Management

#### Create Plugin Instance
```http
POST /plugins/{plugin_name}/instances
Content-Type: application/json

{
  "config": {
    "model_path": "./models/custom.onnx",
    "sample_rate": 16000
  }
}
```

#### List Plugin Instances
```http
GET /plugins/{plugin_name}/instances
```

#### Delete Plugin Instance
```http
DELETE /plugins/{plugin_name}/instances/{instance_id}
```

## Plugin Development

### Creating a Local Plugin

1. **Create Plugin Directory Structure:**
```bash
mkdir -p plugins/asr/my_asr_plugin/src/my_asr_plugin
```

2. **Create plugin.json:**
```json
{
  "name": "my_asr_plugin",
  "version": "1.0.0",
  "description": "My Custom ASR Plugin",
  "author": "Your Name",
  "service_type": "asr",
  "plugin_type": "local",
  "package_manager": "uv",
  "plugin_json_schema": {
    "type": "object",
    "properties": {
      "model_path": {"type": "string"}
    }
  }
}
```

3. **Create pyproject.toml:**
```toml
[project]
name = "my-asr-plugin"
version = "1.0.0"
dependencies = [
    "fastapi",
    "uvicorn",
    "numpy"
]

[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"
```

4. **Implement ASR Engine:**
```python
# src/my_asr_plugin/engine.py
from plugins.asr.asr_engine_interface import ASREngineInterface
import numpy as np

class MyASREngine(ASREngineInterface):
    async def initialize(self, config):
        self.model_path = config["model_path"]
        # Initialize your model here
        self._is_initialized = True

    async def transcribe(self, audio: np.ndarray, custom=None) -> str:
        # Implement transcription logic
        return "transcribed text"

    def is_ready(self) -> bool:
        return self._is_initialized

    async def cleanup(self):
        # Cleanup resources
        pass
```

5. **Create Server:**
```python
# src/my_asr_plugin/server.py
import os
os.environ["ASR_ENGINE_MODULE"] = "my_asr_plugin.engine"
os.environ["ASR_ENGINE_CLASS"] = "MyASREngine"

from plugins.asr.asr_server import app

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("my_asr_plugin.server:app", host="0.0.0.0", port=8000)
```

### Creating a Remote Plugin

Remote plugins must implement the same API endpoints as local plugins:

#### Required Endpoints:
- `GET /health` - Health check
- `GET /plugin-config` - Return plugin.json configuration
- `POST /create_instance` - Create new instance
- `GET /list_instances` - List instances
- `DELETE /delete_instance/{instance_id}` - Delete instance
- `POST /transcribe` - Service-specific endpoint (for ASR)

#### Example Remote Plugin Server:
```python
from fastapi import FastAPI
import json

app = FastAPI()

@app.get("/health")
async def health():
    return {"status": "healthy"}

@app.get("/plugin-config")
async def get_plugin_config():
    return {
        "name": "my_remote_asr",
        "version": "1.0.0",
        "service_type": "asr",
        "plugin_type": "remote",
        "base_url": "http://localhost:8080"
    }

@app.post("/create_instance")
async def create_instance(request: dict):
    # Implement instance creation
    return {"instance_id": "instance_123"}

# ... implement other required endpoints
```

## Configuration Reference

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `OLV_LAUNCHER_HOST` | `127.0.0.1` | Server host address |
| `OLV_LAUNCHER_PORT` | `7000` | Server port |
| `OLV_LAUNCHER_PLUGINS_DIR` | `plugins` | Local plugins directory |
| `OLV_LAUNCHER_REMOTE_PLUGINS` | `[]` | List of remote plugin URLs |
| `OLV_LAUNCHER_REMOTE_PLUGIN_TIMEOUT` | `10` | Remote plugin timeout (seconds) |
| `OLV_LAUNCHER_PLUGIN_STARTUP_TIMEOUT` | `600` | Local plugin startup timeout |
| `OLV_LAUNCHER_DEFAULT_PORT_RANGES` | `["8001-8020", "9001-9020", "10001-10020"]` | Port ranges for local plugins |

### Plugin Configuration Schema

#### Required Fields:
- `name`: Unique plugin identifier
- `version`: Plugin version
- `service_type`: One of "asr", "tts", "llm"
- `plugin_type`: "local" or "remote"

#### Optional Fields:
- `description`: Plugin description
- `author`: Plugin author
- `base_url`: Required for remote plugins
- `package_manager`: Package manager for local plugins (default: "uv")
- `plugin_json_schema`: JSON Schema for configuration validation
- `plugin_ui_schema`: UI Schema for form generation

## Troubleshooting

### Common Issues

1. **Plugin Not Discovered:**
   - Check plugin.json syntax
   - Ensure plugin_type is set correctly
   - For local plugins, verify pyproject.toml exists

2. **Remote Plugin Connection Failed:**
   - Verify base_url is accessible
   - Check remote plugin implements required endpoints
   - Increase remote_plugin_timeout if needed

3. **Local Plugin Startup Failed:**
   - Check plugin dependencies are installed
   - Verify UV is available for environment management
   - Check plugin logs for detailed error messages

### Logs and Debugging

- Plugin logs are available via `/plugins/{plugin_name}/logs`
- Use `/plugins/{plugin_name}/health` to check plugin status
- Enable debug logging by setting `OLV_LAUNCHER_LOG_LEVEL=DEBUG`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Add tests and documentation
5. Submit a pull request

## License

[Add your license information here]