"""
Pydantic models for OLV Launcher API.

This module defines request and response models used throughout the FastAPI
application to ensure proper data validation and serialization.
"""

from typing import List, Dict, Optional, Any
from pydantic import BaseModel, Field
from enum import Enum


# Status Enums
class PluginStatus(str, Enum):
    """Plugin service status enumeration"""
    RUNNING = "running"
    STOPPED = "stopped"
    ERROR = "error"
    STARTING = "starting"
    STOPPING = "stopping"


# Request Models
class PortConfigRequest(BaseModel):
    """Request model for configuring port ranges"""

    port_ranges: List[str] = Field(
        ...,
        description="List of port ranges in format 'start-end' or single ports",
        example=["8001-8005", "12000-12010", "9000"],
    )


class PluginInstanceCreateRequest(BaseModel):
    """Request model for creating a plugin instance"""

    config: Dict[str, Any] = Field(
        ..., description="Complete configuration for the plugin instance matching the plugin's JSON schema"
    )


# Response Models
class HealthResponse(BaseModel):
    """Health check response model"""

    status: PluginStatus = Field(..., description="System health status")
    service: str = Field(..., description="Service name")
    total_plugins: int = Field(..., description="Total number of discovered plugins")
    running_services: int = Field(..., description="Number of running plugin services")
    port_status: Dict[str, Any] = Field(..., description="Port allocation status")
    timestamp: int = Field(..., description="Response timestamp")


class ErrorResponse(BaseModel):
    """Error response model"""

    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Human-readable error message")
    detail: Optional[str] = Field(None, description="Detailed error information")


class PluginApiInfo(BaseModel):
    """Plugin information model for API responses"""

    name: str = Field(..., description="Plugin name")
    service_type: str = Field(..., description="Service type")
    version: str = Field(..., description="Plugin version")
    description: str = Field(..., description="Plugin description")
    author: str = Field(..., description="Plugin author")
    status: PluginStatus = Field(..., description="Plugin service status")
    service_url: Optional[str] = Field(None, description="Complete service URL for accessing the plugin")
    is_local: bool = Field(..., description="Whether the plugin is local or remote")


class PluginListResponse(BaseModel):
    """Response model for listing plugins"""

    plugins: List[PluginApiInfo] = Field(..., description="List of plugins")


class ServicePluginListResponse(BaseModel):
    """Response model for listing plugins by service type"""

    service_type: str = Field(..., description="Service type")
    plugins: List[PluginApiInfo] = Field(..., description="List of plugins")


class PluginStartResponse(BaseModel):
    """Response model for starting a plugin"""

    message: str = Field(..., description="Success message")
    plugin_name: str = Field(..., description="Plugin name")
    port: int = Field(..., description="Assigned port number")
    status: PluginStatus = Field(..., description="Plugin status")


class PluginStopResponse(BaseModel):
    """Response model for stopping a plugin"""

    message: str = Field(..., description="Success message")
    plugin_name: str = Field(..., description="Plugin name")
    status: PluginStatus = Field(..., description="Plugin status")


class PluginHealthResponse(BaseModel):
    """Response model for plugin health check"""

    plugin_name: str = Field(..., description="Plugin name")
    status: PluginStatus = Field(..., description="Plugin status")
    port: Optional[int] = Field(None, description="Port number if available")
    http_status: Optional[int] = Field(None, description="HTTP status code")
    error: Optional[str] = Field(None, description="Error message if any")
    message: Optional[str] = Field(None, description="Additional message")


class PluginSchemasResponse(BaseModel):
    """Response model for all plugin schemas"""

    plugin_name: str = Field(..., description="Plugin name")
    schemas: Dict[str, Dict] = Field(
        ...,
        description="All plugin configuration schemas including JSON and UI schemas",
    )


class PluginSchemaResponse(BaseModel):
    """Response model for plugin schemas"""

    plugin_name: str = Field(..., description="Plugin name")
    json_schema: Dict = Field(..., description="JSON schema for plugin configuration")
    ui_schema: Dict = Field(..., description="UI schema for plugin configuration")


class PortStatusResponse(BaseModel):
    """Response model for port status"""

    allocated_ports: Dict[str, int] = Field(
        ..., description="Currently allocated ports"
    )
    available_ports: List[int] = Field(..., description="Available ports")
    total_ranges: List[str] = Field(..., description="Configured port ranges")


class PortConfigResponse(BaseModel):
    """Response model for port configuration"""

    message: str = Field(..., description="Success message")
    port_ranges: List[str] = Field(..., description="Configured port ranges")
    status: PortStatusResponse = Field(..., description="Current port status")


class AvailablePortsResponse(BaseModel):
    """Response model for available ports"""

    available_ports: List[int] = Field(..., description="List of available ports")
    total_available: int = Field(..., description="Total number of available ports")


class PluginLogsResponse(BaseModel):
    """Response model for plugin logs"""

    plugin_name: str = Field(..., description="Plugin name")
    logs: List[str] = Field(..., description="List of log lines")
    total_lines: int = Field(..., description="Total number of log lines returned")
    has_more: bool = Field(..., description="Whether there are more logs available")


class PluginLogsClearResponse(BaseModel):
    """Response model for clearing plugin logs"""

    message: str = Field(..., description="Success message")
    plugin_name: str = Field(..., description="Plugin name")


class PluginForceCleanupResponse(BaseModel):
    """Response model for force cleanup of plugin"""

    message: str = Field(..., description="Success message")
    plugin_name: str = Field(..., description="Plugin name")
    status: str = Field(..., description="Plugin status after cleanup")


class PluginInstanceCreateResponse(BaseModel):
    """Response model for creating a plugin instance"""

    message: str = Field(..., description="Success message")
    plugin_name: str = Field(..., description="Plugin name")
    instance_id: str = Field(..., description="Created instance ID")
    config: Dict[str, Any] = Field(..., description="Applied configuration")


class PluginInstanceListResponse(BaseModel):
    """Response model for listing plugin instances"""

    plugin_name: str = Field(..., description="Plugin name")
    instances: Dict[str, Any] = Field(..., description="Dictionary of instance information")


class PluginInstanceDeleteResponse(BaseModel):
    """Response model for deleting a plugin instance"""

    message: str = Field(..., description="Success message")
    plugin_name: str = Field(..., description="Plugin name")
    instance_id: str = Field(..., description="Deleted instance ID")
