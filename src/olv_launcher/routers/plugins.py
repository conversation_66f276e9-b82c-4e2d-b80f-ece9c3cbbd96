"""
Plugin management routes for OLV Launcher.

This module contains all endpoints related to plugin discovery, management,
health checking, and configuration schema retrieval.
"""

import httpx
from fastapi import APIRouter, HTTPException, status, Path

from src.olv_launcher.dependencies import PluginManagerDep
from src.olv_launcher.plugin_manager import ServiceType
from src.olv_launcher.exceptions import PluginNotFoundError
from src.olv_launcher.models import (
    PluginListResponse,
    ServicePluginListResponse,
    PluginStartResponse,
    PluginStopResponse,
    PluginHealthResponse,
    PluginSchemasResponse,
    PluginSchemaResponse,
    PluginLogsResponse,
    PluginLogsClearResponse,
    PluginForceCleanupResponse,
    PluginApiInfo,
    PluginInstanceCreateRequest,
    PluginInstanceCreateResponse,
    PluginInstanceListResponse,
    PluginInstanceDeleteResponse,
    PluginStatus,
)

router = APIRouter()


@router.get(
    "/",
    response_model=PluginListResponse,
    summary="List all plugins",
    description="Get a list of all discovered plugins across all service types",
)
async def list_all_plugins(manager: PluginManagerDep) -> PluginListResponse:
    """List all discovered plugins across all service types"""
    plugins_data = manager.get_all_plugins()
    plugins = [PluginApiInfo(**plugin) for plugin in plugins_data]
    return PluginListResponse(plugins=plugins)


@router.get(
    "/{service_type}",
    response_model=ServicePluginListResponse,
    summary="List plugins by service type",
    description="Get a list of plugins for a specific service type",
)
async def list_plugins_by_service(
    service_type: str = Path(..., description="Service type (asr, tts, llm)"),
    manager: PluginManagerDep = None,
) -> ServicePluginListResponse:
    """List plugins for a specific service type"""
    try:
        service_enum = ServiceType(service_type)
        plugins_data = manager.get_plugins_by_service(service_enum)
        plugins = [PluginApiInfo(**plugin) for plugin in plugins_data]

        return ServicePluginListResponse(service_type=service_type, plugins=plugins)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid service type: {service_type}. Valid types: {[s.value for s in ServiceType]}",
        )


@router.post(
    "/{plugin_name}/start",
    response_model=PluginStartResponse,
    summary="Start plugin",
    description="Start a specific plugin service",
)
async def start_plugin(
    plugin_name: str = Path(..., description="Name of the plugin to start"),
    manager: PluginManagerDep = None,
) -> PluginStartResponse:
    """Start a specific plugin service"""
    try:
        port = await manager.start_plugin_service(plugin_name)
        return PluginStartResponse(
            message=f"Plugin '{plugin_name}' started successfully",
            plugin_name=plugin_name,
            port=port,
            status=PluginStatus.RUNNING,
        )
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start plugin: {e}",
        )


@router.post(
    "/{plugin_name}/stop",
    response_model=PluginStopResponse,
    summary="Stop plugin",
    description="Stop a specific plugin service",
)
async def stop_plugin(
    plugin_name: str = Path(..., description="Name of the plugin to stop"),
    manager: PluginManagerDep = None,
) -> PluginStopResponse:
    """Stop a specific plugin service"""
    try:
        manager.stop_plugin_service(plugin_name)
        return PluginStopResponse(
            message=f"Plugin '{plugin_name}' stopped successfully",
            plugin_name=plugin_name,
            status=PluginStatus.STOPPED,
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to stop plugin: {e}",
        )


@router.get(
    "/{plugin_name}/health",
    response_model=PluginHealthResponse,
    summary="Check plugin health",
    description="Check the health status of a specific plugin",
)
async def check_plugin_health(
    plugin_name: str = Path(..., description="Name of the plugin to check"),
    manager: PluginManagerDep = None,
) -> PluginHealthResponse:
    """Check health of a specific plugin"""
    if plugin_name not in manager.plugins:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Plugin '{plugin_name}' not found",
        )

    if not manager.is_plugin_running(plugin_name):
        return PluginHealthResponse(plugin_name=plugin_name, status=PluginStatus.STOPPED)

    port = manager.get_plugin_port(plugin_name)
    if port is None:
        return PluginHealthResponse(
            plugin_name=plugin_name,
            status=PluginStatus.ERROR,
            message="Plugin running but port not found",
        )

    try:
        async with httpx.AsyncClient(timeout=httpx.Timeout(5.0)) as client:
            response = await client.get(f"http://127.0.0.1:{port}/health")
            if response.status_code == 200:
                return PluginHealthResponse(
                    plugin_name=plugin_name,
                    status=PluginStatus.RUNNING,
                    port=port,
                    http_status=response.status_code,
                )
            else:
                return PluginHealthResponse(
                    plugin_name=plugin_name,
                    status=PluginStatus.ERROR,
                    port=port,
                    http_status=response.status_code,
                )
    except Exception as e:
        return PluginHealthResponse(
            plugin_name=plugin_name, status=PluginStatus.ERROR, port=port, error=str(e)
        )


def _map_plugin_status_to_health(plugin_status: str) -> PluginStatus:
    """Map plugin-reported status to our simplified status enum"""
    status_mapping = {
        "healthy": PluginStatus.RUNNING,
        "running": PluginStatus.RUNNING,
        "stopped": PluginStatus.STOPPED,
        "error": PluginStatus.ERROR,
        "starting": PluginStatus.STARTING,
        "stopping": PluginStatus.STOPPING,
    }
    return status_mapping.get(plugin_status, PluginStatus.ERROR)


@router.get(
    "/{plugin_name}/schemas",
    response_model=PluginSchemasResponse,
    summary="Get all plugin schemas",
    description="Get all JSON and UI schemas for a specific plugin",
)
async def get_plugin_schemas(
    plugin_name: str = Path(..., description="Name of the plugin"),
    manager: PluginManagerDep = None,
) -> PluginSchemasResponse:
    """Get all configuration schemas for a specific plugin"""
    try:
        schemas = await manager.get_plugin_schemas(plugin_name)
        return PluginSchemasResponse(plugin_name=plugin_name, schemas=schemas)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get schemas: {e}",
        )


@router.get(
    "/{plugin_name}/schemas/plugin",
    response_model=PluginSchemaResponse,
    summary="Get plugin schemas",
    description="Get both JSON and UI schemas for plugin configuration",
)
async def get_plugin_schemas_detailed(
    plugin_name: str = Path(..., description="Name of the plugin"),
    manager: PluginManagerDep = None,
) -> PluginSchemaResponse:
    """Get plugin JSON and UI schemas for a specific plugin"""
    try:
        all_schemas = await manager.get_plugin_schemas(plugin_name)
        return PluginSchemaResponse(
            plugin_name=plugin_name,
            json_schema=all_schemas.get("plugin_json_schema", {}),
            ui_schema=all_schemas.get("plugin_ui_schema", {}),
        )
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get plugin schemas: {e}",
        )


@router.get(
    "/{plugin_name}/schemas/plugin/json",
    summary="Get plugin JSON schema",
    description="Get only the JSON schema for plugin configuration",
)
async def get_plugin_json_schema(
    plugin_name: str = Path(..., description="Name of the plugin"),
    manager: PluginManagerDep = None,
) -> dict:
    """Get plugin JSON schema for a specific plugin"""
    try:
        all_schemas = await manager.get_plugin_schemas(plugin_name)
        return all_schemas.get("plugin_json_schema", {})
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get plugin JSON schema: {e}",
        )


@router.get(
    "/{plugin_name}/schemas/plugin/ui",
    summary="Get plugin UI schema",
    description="Get only the UI schema for plugin configuration",
)
async def get_plugin_ui_schema(
    plugin_name: str = Path(..., description="Name of the plugin"),
    manager: PluginManagerDep = None,
) -> dict:
    """Get plugin UI schema for a specific plugin"""
    try:
        all_schemas = await manager.get_plugin_schemas(plugin_name)
        return all_schemas.get("plugin_ui_schema", {})
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get plugin UI schema: {e}",
        )


@router.get(
    "/{plugin_name}/logs",
    response_model=PluginLogsResponse,
    summary="Get plugin logs",
    description="Get recent log lines from a specific plugin",
)
async def get_plugin_logs(
    plugin_name: str = Path(..., description="Name of the plugin"),
    lines: int = 100,
    manager: PluginManagerDep = None,
) -> PluginLogsResponse:
    """Get recent logs for a specific plugin"""
    try:
        logs = manager.get_plugin_logs(plugin_name, lines)
        return PluginLogsResponse(
            plugin_name=plugin_name,
            logs=logs,
            total_lines=len(logs),
            has_more=False,  # For simplicity, we don't implement pagination yet
        )
    except PluginNotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get logs: {e}",
        )


@router.delete(
    "/{plugin_name}/logs",
    response_model=PluginLogsClearResponse,
    summary="Clear plugin logs",
    description="Clear stored logs for a specific plugin",
)
async def clear_plugin_logs(
    plugin_name: str = Path(..., description="Name of the plugin"),
    manager: PluginManagerDep = None,
) -> PluginLogsClearResponse:
    """Clear stored logs for a specific plugin"""
    try:
        manager.clear_plugin_logs(plugin_name)
        return PluginLogsClearResponse(
            message=f"Logs cleared for plugin '{plugin_name}'", plugin_name=plugin_name
        )
    except PluginNotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to clear logs: {e}",
        )


@router.post(
    "/{plugin_name}/force-cleanup",
    response_model=PluginForceCleanupResponse,
    summary="Force cleanup plugin",
    description="Force cleanup of a plugin's state, even if it appears to be running. Use when a plugin is stuck in 'running' state but not actually functioning.",
)
async def force_cleanup_plugin(
    plugin_name: str = Path(..., description="Name of the plugin to force cleanup"),
    manager: PluginManagerDep = None,
) -> PluginForceCleanupResponse:
    """Force cleanup of a plugin's state and resources"""
    try:
        manager.force_cleanup_plugin(plugin_name)
        return PluginForceCleanupResponse(
            message=f"Plugin '{plugin_name}' force cleaned up successfully",
            plugin_name=plugin_name,
            status=PluginStatus.STOPPED.value,
        )
    except PluginNotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to force cleanup plugin: {e}",
        )


@router.post(
    "/{plugin_name}/instances",
    response_model=PluginInstanceCreateResponse,
    summary="Create plugin instance",
    description="Create a new instance for a specific plugin with given configuration",
)
async def create_plugin_instance(
    plugin_name: str = Path(..., description="Name of the plugin"),
    request: PluginInstanceCreateRequest = None,
    manager: PluginManagerDep = None,
) -> PluginInstanceCreateResponse:
    """Create a new instance for a specific plugin"""
    try:
        instance_id = await manager.create_plugin_instance(plugin_name, request.config)
        return PluginInstanceCreateResponse(
            message=f"Instance created successfully for plugin '{plugin_name}'",
            plugin_name=plugin_name,
            instance_id=instance_id,
            config=request.config,
        )
    except PluginNotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create instance: {e}",
        )


@router.get(
    "/{plugin_name}/instances",
    response_model=PluginInstanceListResponse,
    summary="List plugin instances",
    description="List all instances for a specific plugin",
)
async def list_plugin_instances(
    plugin_name: str = Path(..., description="Name of the plugin"),
    manager: PluginManagerDep = None,
) -> PluginInstanceListResponse:
    """List all instances for a specific plugin"""
    try:
        instances_data = await manager.list_plugin_instances(plugin_name)
        return PluginInstanceListResponse(
            plugin_name=plugin_name,
            instances=instances_data,
        )
    except PluginNotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list instances: {e}",
        )


@router.delete(
    "/{plugin_name}/instances/{instance_id}",
    response_model=PluginInstanceDeleteResponse,
    summary="Delete plugin instance",
    description="Delete a specific instance for a plugin",
)
async def delete_plugin_instance(
    plugin_name: str = Path(..., description="Name of the plugin"),
    instance_id: str = Path(..., description="ID of the instance to delete"),
    manager: PluginManagerDep = None,
) -> PluginInstanceDeleteResponse:
    """Delete a specific instance for a plugin"""
    try:
        success = await manager.delete_plugin_instance(plugin_name, instance_id)
        if success:
            return PluginInstanceDeleteResponse(
                message=f"Instance '{instance_id}' deleted successfully for plugin '{plugin_name}'",
                plugin_name=plugin_name,
                instance_id=instance_id,
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Instance '{instance_id}' not found for plugin '{plugin_name}'",
            )
    except PluginNotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete instance: {e}",
        )
