"""
Plugin management for OLV Launcher.

This module provides unified plugin management capabilities across all service
types (ASR, TTS, LLM) with proper error handling, port management,
and lifecycle management.
"""

import asyncio
import json
import os
import subprocess
import time
import threading
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import httpx
import logging

from .models import ServiceType, PluginInfo, PluginStatus
from .models.plugin import PluginType
from .port_mananger import PortManager
from .exceptions import (
    PluginNotFoundError,
    PluginAlreadyRunningError,
    PluginNotRunningError,
    ServiceError,
    PortAllocationError,
)
from .models.config import settings

logger = logging.getLogger(__name__)


class UnifiedPluginManager:
    """
    Unified plugin manager for all service types.

    This class manages the discovery, lifecycle, and port allocation
    for plugins across all supported service types.
    """

    def __init__(self, plugins_dir: str = "plugins", port_ranges: List[str] = None):
        """
        Initialize the unified plugin manager.

        Args:
            plugins_dir: Directory containing plugin subdirectories
            port_ranges: List of port ranges for allocation
        """
        self.plugins_dir = Path(plugins_dir)

        if port_ranges is None:
            port_ranges = ["8001-8100"]

        self.port_manager = PortManager(port_ranges)

        # plugin_name -> PluginInfo
        self.plugins: Dict[str, PluginInfo] = {}

        # plugin_name -> (process, port)
        self.running_services: Dict[str, Tuple[subprocess.Popen, int]] = {}

        # plugin_name -> PluginStatus (for tracking transitional states)
        self.plugin_statuses: Dict[str, PluginStatus] = {}

        # plugin_name -> List[log_line] (keeping recent logs for real-time display)
        self.plugin_logs: Dict[str, List[str]] = {}

        # plugin_name -> Thread (log reader threads)
        self.log_reader_threads: Dict[str, threading.Thread] = {}

        # service_type -> List[plugin_name]
        self.plugins_by_service: Dict[ServiceType, List[str]] = {
            service_type: [] for service_type in ServiceType
        }

        self.discover_plugins()
        asyncio.create_task(self.discover_remote_plugins())

    def _read_plugin_logs(self, plugin_name: str, process: subprocess.Popen) -> None:
        """Read logs from plugin process and store them"""

        def read_output(stream, stream_name):
            """Read from stdout or stderr stream"""
            try:
                for line in iter(stream.readline, ""):
                    if line:
                        log_line = line.strip()
                        if log_line:  # Skip empty lines
                            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
                            formatted_line = f"[{timestamp}] [{plugin_name}] {log_line}"

                            # Log to main logger (all as info for simplicity)
                            logger.info(formatted_line)

                            # Store in plugin logs (keep last 1000 lines)
                            if plugin_name not in self.plugin_logs:
                                self.plugin_logs[plugin_name] = []
                            self.plugin_logs[plugin_name].append(formatted_line)
                            if len(self.plugin_logs[plugin_name]) > 1000:
                                self.plugin_logs[plugin_name] = self.plugin_logs[
                                    plugin_name
                                ][-1000:]
            except Exception as e:
                logger.error(f"Error reading {stream_name} for {plugin_name}: {e}")

        # Start reader threads for both stdout and stderr
        if process.stdout:
            stdout_thread = threading.Thread(
                target=read_output, args=(process.stdout, "stdout"), daemon=True
            )
            stdout_thread.start()
            self.log_reader_threads[f"{plugin_name}_stdout"] = stdout_thread

        if process.stderr:
            stderr_thread = threading.Thread(
                target=read_output, args=(process.stderr, "stderr"), daemon=True
            )
            stderr_thread.start()
            self.log_reader_threads[f"{plugin_name}_stderr"] = stderr_thread

    def discover_plugins(self) -> None:
        """Discover all available local plugins across all service types"""
        if not self.plugins_dir.exists():
            logger.warning(f"Plugins directory {self.plugins_dir} does not exist")
            return

        logger.info(f"Discovering local plugins in {self.plugins_dir}")

        # Scan service type directories
        for service_dir in self.plugins_dir.iterdir():
            if not service_dir.is_dir():
                continue

            try:
                service_type = ServiceType(service_dir.name)
            except ValueError:
                logger.warning(f"Unknown service type directory: {service_dir.name}")
                continue

            # Scan plugins within service type
            for plugin_dir in service_dir.iterdir():
                if not plugin_dir.is_dir():
                    continue

                try:
                    self._discover_local_plugin(plugin_dir, service_type)
                except Exception as e:
                    logger.error(f"Failed to discover local plugin {plugin_dir.name}: {e}")

    async def discover_remote_plugins(self) -> None:
        """Discover remote plugins from configured URLs"""
        if not settings.remote_plugins:
            logger.debug("No remote plugins configured")
            return

        logger.info(f"Discovering remote plugins from {len(settings.remote_plugins)} URLs")

        for base_url in settings.remote_plugins:
            try:
                await self._discover_remote_plugin(base_url)
            except Exception as e:
                logger.error(f"Failed to discover remote plugin at {base_url}: {e}")

    async def _discover_remote_plugin(self, base_url: str) -> None:
        """Discover a single remote plugin"""
        try:
            async with httpx.AsyncClient(timeout=httpx.Timeout(settings.remote_plugin_timeout)) as client:
                # Try to get plugin configuration
                response = await client.get(f"{base_url}/plugin-config")
                if response.status_code != 200:
                    logger.warning(f"Remote plugin at {base_url} returned status {response.status_code}")
                    return

                config_data = response.json()

                # Extract plugin name from URL or config
                plugin_name = config_data.get("name")
                if not plugin_name:
                    # Generate name from URL
                    plugin_name = base_url.replace("http://", "").replace("https://", "").replace(":", "_").replace("/", "_")

                # Add base_url to config and set plugin_type to remote
                config_data["base_url"] = base_url
                config_data["plugin_type"] = "remote"

                plugin_info = PluginInfo.from_remote_config(plugin_name, config_data)

                self.plugins[plugin_name] = plugin_info
                self.plugins_by_service[plugin_info.service_type].append(plugin_name)

                logger.info(f"Discovered remote {plugin_info.service_type.value} plugin: {plugin_name} at {base_url}")

        except httpx.RequestError as e:
            logger.warning(f"Failed to connect to remote plugin at {base_url}: {e}")
        except Exception as e:
            logger.error(f"Error discovering remote plugin at {base_url}: {e}")

    def _discover_local_plugin(
        self, plugin_dir: Path, expected_service_type: ServiceType
    ) -> None:
        """Discover a single local plugin"""
        config_file = plugin_dir / "plugin.json"
        pyproject_file = plugin_dir / "pyproject.toml"

        if not (config_file.exists() and pyproject_file.exists()):
            logger.debug(
                f"Skipping {plugin_dir.name}: missing plugin.json or pyproject.toml"
            )
            return

        plugin_info = PluginInfo.from_config_file(plugin_dir.name, config_file)

        # Validate service type matches directory
        if plugin_info.service_type != expected_service_type:
            logger.warning(
                f"Plugin {plugin_dir.name} service_type mismatch: "
                f"expected {expected_service_type.value}, got {plugin_info.service_type.value}"
            )
            return

        self.plugins[plugin_dir.name] = plugin_info
        self.plugins_by_service[plugin_info.service_type].append(plugin_dir.name)

        logger.info(
            f"Discovered {plugin_info.service_type.value} plugin: {plugin_dir.name}"
        )

        # Ensure plugin environment is synced
        try:
            self._sync_plugin_environment(plugin_dir)
        except Exception as e:
            logger.warning(f"Failed to sync environment for {plugin_dir.name}: {e}")

    def _sync_plugin_environment(self, plugin_dir: Path) -> None:
        """Sync the plugin's UV environment"""
        if not settings.enable_uv_sync:
            logger.debug("UV sync disabled, skipping environment sync")
            return

        try:
            result = subprocess.run(
                ["uv", "sync"],
                cwd=plugin_dir,
                capture_output=True,
                text=True,
                timeout=settings.uv_sync_timeout,
            )
            if result.returncode != 0:
                logger.error(
                    f"Failed to sync environment for {plugin_dir.name}: {result.stderr}"
                )
        except subprocess.TimeoutExpired:
            logger.error(f"Timeout syncing environment for {plugin_dir.name}")
        except FileNotFoundError:
            logger.warning("UV not found, skipping environment sync")
        except Exception as e:
            logger.error(f"Error syncing environment for {plugin_dir.name}: {e}")

    def _cleanup_plugin_resources(self, plugin_name: str, preserve_logs: bool = True, terminate_process: bool = False) -> None:
        """
        Unified method to clean up plugin resources.

        Args:
            plugin_name: Name of the plugin to clean up
            preserve_logs: Whether to preserve plugin logs (default: True for crash scenarios)
            terminate_process: Whether to actively terminate the process (default: False for dead processes)
        """
        if plugin_name not in self.running_services:
            logger.debug(f"Plugin '{plugin_name}' not in running services, nothing to clean up")
            return

        try:
            process, port = self.running_services[plugin_name]

            # Handle process termination if needed
            if terminate_process and process.poll() is None:
                logger.info(f"Terminating process for plugin '{plugin_name}'")
                try:
                    # First try graceful termination
                    process.terminate()
                    try:
                        process.wait(timeout=10)
                        logger.debug(f"Plugin '{plugin_name}' terminated gracefully")
                    except subprocess.TimeoutExpired:
                        # Force kill if graceful termination fails
                        logger.warning(f"Plugin '{plugin_name}' did not terminate gracefully, force killing")
                        process.kill()
                        process.wait()
                        logger.debug(f"Plugin '{plugin_name}' force killed")
                except Exception as e:
                    logger.error(f"Error terminating process for {plugin_name}: {e}")
                    # Try force kill as last resort
                    try:
                        process.kill()
                        process.wait()
                        logger.warning(f"Force killed plugin '{plugin_name}' as last resort")
                    except Exception as kill_error:
                        logger.error(f"Failed to force kill plugin {plugin_name}: {kill_error}")

            # Release port allocation
            self.port_manager.release_port(plugin_name)

            # Remove from running services
            del self.running_services[plugin_name]

            # Clear plugin status
            if plugin_name in self.plugin_statuses:
                del self.plugin_statuses[plugin_name]

            # Clean up logs only if not preserving them
            if not preserve_logs and plugin_name in self.plugin_logs:
                del self.plugin_logs[plugin_name]
                logger.debug(f"Cleared logs for plugin '{plugin_name}'")

            # Clean up log reader threads
            threads_to_remove = [
                key for key in self.log_reader_threads.keys() if key.startswith(plugin_name)
            ]
            for thread_key in threads_to_remove:
                del self.log_reader_threads[thread_key]

            logger.info(f"Successfully cleaned up resources for plugin '{plugin_name}'")

        except Exception as e:
            logger.error(f"Error cleaning up resources for {plugin_name}: {e}")
            # Even if cleanup fails, try to remove from running_services
            if plugin_name in self.running_services:
                del self.running_services[plugin_name]

    def _cleanup_dead_processes(self) -> None:
        """Clean up dead processes from running_services"""
        dead_plugins = []

        for plugin_name, (process, port) in self.running_services.items():
            if process.poll() is not None:  # Process has died
                dead_plugins.append(plugin_name)
                logger.warning(f"Detected dead process for plugin '{plugin_name}', cleaning up")

        for plugin_name in dead_plugins:
            # Preserve logs when cleaning up crashed processes
            self._cleanup_plugin_resources(plugin_name, preserve_logs=True, terminate_process=False)

    async def start_plugin_service(self, plugin_name: str) -> int:
        """
        Start a plugin service and return the port number.
        For remote plugins, this just checks if they're available.

        Args:
            plugin_name: Name of the plugin to start

        Returns:
            int: Port number the service is running on (for local plugins)
                 or 0 for remote plugins (they don't use local ports)

        Raises:
            PluginNotFoundError: If plugin doesn't exist
            PluginAlreadyRunningError: If plugin is already running
            ServiceError: If service fails to start
        """
        if plugin_name not in self.plugins:
            raise PluginNotFoundError(f"Plugin '{plugin_name}' not found")

        plugin_info = self.plugins[plugin_name]

        # Handle remote plugins differently
        if plugin_info.is_remote:
            return await self._start_remote_plugin(plugin_name, plugin_info)
        else:
            return await self._start_local_plugin(plugin_name, plugin_info)

    async def _start_remote_plugin(self, plugin_name: str, plugin_info: PluginInfo) -> int:
        """Start/check a remote plugin"""
        # Set status to STARTING
        self.plugin_statuses[plugin_name] = PluginStatus.STARTING

        try:
            # Check if remote plugin is available
            async with httpx.AsyncClient(timeout=httpx.Timeout(settings.remote_plugin_timeout)) as client:
                response = await client.get(f"{plugin_info.base_url}/health")
                if response.status_code == 200:
                    # Remote plugin is available
                    self.plugin_statuses[plugin_name] = PluginStatus.RUNNING
                    logger.info(f"Remote plugin '{plugin_name}' is available at {plugin_info.base_url}")
                    return 0  # Remote plugins don't use local ports
                else:
                    raise ServiceError(f"Remote plugin '{plugin_name}' health check failed: HTTP {response.status_code}")
        except httpx.RequestError as e:
            # Clear starting status on failure
            if plugin_name in self.plugin_statuses:
                del self.plugin_statuses[plugin_name]
            raise ServiceError(f"Failed to connect to remote plugin '{plugin_name}': {e}")
        except Exception as e:
            # Clear starting status on failure
            if plugin_name in self.plugin_statuses:
                del self.plugin_statuses[plugin_name]
            raise ServiceError(f"Failed to start remote plugin '{plugin_name}': {e}")

    async def _start_local_plugin(self, plugin_name: str, plugin_info: PluginInfo) -> int:
        """Start a local plugin"""
        # Clean up any dead processes first
        self._cleanup_dead_processes()

        # Check if plugin is actually running (after cleanup)
        if plugin_name in self.running_services:
            process, port = self.running_services[plugin_name]
            if process.poll() is None:  # Process is still alive
                raise PluginAlreadyRunningError(
                    f"Plugin '{plugin_name}' is already running on port {port}"
                )
            else:
                # Process is dead but wasn't cleaned up, clean it now
                logger.warning(f"Plugin '{plugin_name}' process was dead, cleaning up before restart")
                self._cleanup_plugin_resources(plugin_name, preserve_logs=True, terminate_process=False)

        # Set status to STARTING
        self.plugin_statuses[plugin_name] = PluginStatus.STARTING

        try:
            port = self.port_manager.allocate_port(plugin_name)
        except PortAllocationError as e:
            # Clear starting status on failure
            if plugin_name in self.plugin_statuses:
                del self.plugin_statuses[plugin_name]
            raise ServiceError(
                f"Failed to allocate port for plugin '{plugin_name}': {e}"
            )

        # Start the FastAPI service using UV
        cmd = [
            "uv",
            "run",
            "--project",
            str(plugin_info.plugin_dir),
            "python",
            "-m",
            "uvicorn",
            f"{plugin_name}.server:app",
            "--host",
            "127.0.0.1",
            "--port",
            str(port),
            "--log-level",
            "info",
            "--access-log",
            "--no-use-colors",  # Disable colors for cleaner log parsing
        ]

        try:
            # Set up environment to reduce warnings
            env = dict(os.environ)
            env.pop("VIRTUAL_ENV", None)  # Remove VIRTUAL_ENV to avoid UV warnings

            process = subprocess.Popen(
                cmd,
                cwd=plugin_info.plugin_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,  # Line buffered
                universal_newlines=True,
                env=env,
            )

            self.running_services[plugin_name] = (process, port)

            # Start log reading threads
            self._read_plugin_logs(plugin_name, process)

            # Wait for service to be ready
            await self._wait_for_service_ready(
                f"http://127.0.0.1:{port}",
                timeout=600,  # 10 minutes
                plugin_name=plugin_name
            )

            # Set status to RUNNING after successful startup
            self.plugin_statuses[plugin_name] = PluginStatus.RUNNING

            logger.info(
                f"Started {plugin_info.service_type.value} plugin '{plugin_name}' on port {port}"
            )
            return port

        except Exception as e:
            # Clean up on failure - preserve logs but terminate process if needed
            logger.error(f"Plugin '{plugin_name}' startup failed: {e}")
            if plugin_name in self.running_services:
                self._cleanup_plugin_resources(plugin_name, preserve_logs=True, terminate_process=True)
            else:
                # If not in running_services, still need to release the port
                self.port_manager.release_port(plugin_name)

            raise ServiceError(f"Failed to start plugin service '{plugin_name}': {e}")

    async def _wait_for_service_ready(self, base_url: str, timeout: int = 600, plugin_name: str = None) -> None:
        """Wait for a service to be ready by checking its health endpoint"""
        start_time = time.time()

        async with httpx.AsyncClient() as client:
            while time.time() - start_time < timeout:
                # Check if the process is still alive (if plugin_name is provided)
                if plugin_name and plugin_name in self.running_services:
                    process, _ = self.running_services[plugin_name]
                    if process.poll() is not None:
                        # Process has died, no point in waiting
                        exit_code = process.poll()
                        raise ServiceError(
                            f"Plugin '{plugin_name}' process crashed during startup (exit code: {exit_code})"
                        )

                try:
                    response = await client.get(f"{base_url}/health", timeout=5.0)
                    if response.status_code == 200:
                        logger.info(f"Plugin '{plugin_name}' health check successful")
                        return
                except Exception as e:
                    logger.debug(f"Health check failed for {plugin_name}: {e}")
                    pass

                await asyncio.sleep(1)

        raise ServiceError(
            f"Service at {base_url} did not become ready within {timeout} seconds"
        )

    def stop_plugin_service(self, plugin_name: str) -> None:
        """
        Stop a running plugin service.
        For remote plugins, this just marks them as stopped.

        Args:
            plugin_name: Name of the plugin to stop

        Raises:
            PluginNotFoundError: If plugin doesn't exist
            PluginNotRunningError: If plugin is not running
        """
        if plugin_name not in self.plugins:
            raise PluginNotFoundError(f"Plugin '{plugin_name}' not found")

        plugin_info = self.plugins[plugin_name]

        # Handle remote plugins differently
        if plugin_info.is_remote:
            # For remote plugins, just update status
            if plugin_name not in self.plugin_statuses or self.plugin_statuses[plugin_name] != PluginStatus.RUNNING:
                raise PluginNotRunningError(f"Remote plugin '{plugin_name}' is not running")

            self.plugin_statuses[plugin_name] = PluginStatus.STOPPED
            logger.info(f"Marked remote plugin '{plugin_name}' as stopped")
        else:
            # For local plugins, check running services
            if plugin_name not in self.running_services:
                raise PluginNotRunningError(f"Plugin '{plugin_name}' is not running")

            # Set status to STOPPING
            self.plugin_statuses[plugin_name] = PluginStatus.STOPPING

            # Clean up with graceful termination and log clearing (normal stop)
            self._cleanup_plugin_resources(plugin_name, preserve_logs=False, terminate_process=True)
            logger.info(f"Stopped local plugin service '{plugin_name}'")

    def stop_all_services(self) -> None:
        """Stop all running plugin services"""
        plugin_names = list(self.running_services.keys())
        for plugin_name in plugin_names:
            try:
                self.stop_plugin_service(plugin_name)
            except Exception as e:
                logger.error(f"Error stopping plugin {plugin_name}: {e}")

    def get_plugins_by_service(self, service_type: ServiceType) -> List[Dict]:
        """Get list of plugins for a specific service type"""
        self._cleanup_dead_processes()

        result = []

        for plugin_name in self.plugins_by_service[service_type]:
            plugin_info = self.plugins[plugin_name]

            # Check if we have an explicit status set (for transitional states)
            if plugin_name in self.plugin_statuses:
                status = self.plugin_statuses[plugin_name]
            else:
                # Default logic for non-transitional states
                if plugin_info.is_remote:
                    # For remote plugins, default to stopped unless explicitly set
                    status = PluginStatus.STOPPED
                else:
                    status = PluginStatus.STOPPED

            port = None
            service_url = None

            if plugin_info.is_remote:
                # Remote plugins use their base_url as service_url
                service_url = plugin_info.base_url
            else:
                # Local plugins
                if plugin_name in self.running_services:
                    process, port = self.running_services[plugin_name]
                    if process.poll() is None:  # Process is still running
                        # Only override status if not in a transitional state
                        if plugin_name not in self.plugin_statuses:
                            status = PluginStatus.RUNNING
                        service_url = f"http://127.0.0.1:{port}"
                    else:
                        # Process is dead
                        status = PluginStatus.ERROR

            result.append(
                {
                    "name": plugin_name,
                    "service_type": service_type.value,
                    "plugin_type": plugin_info.plugin_type.value,
                    "version": plugin_info.version,
                    "description": plugin_info.description,
                    "author": plugin_info.author,
                    "status": status.value,
                    "port": port,
                    "service_url": service_url,
                    "base_url": plugin_info.base_url,
                }
            )

        return result

    def get_all_plugins(self) -> List[Dict]:
        """Get list of all plugins across all services"""
        result = []
        for service_type in ServiceType:
            result.extend(self.get_plugins_by_service(service_type))
        return result

    async def get_plugin_schemas(self, plugin_name: str) -> Dict[str, dict]:
        """
        Get configuration schemas for a plugin.

        Args:
            plugin_name: Name of the plugin

        Returns:
            Dict containing plugin_json_schema, plugin_ui_schema

        Raises:
            PluginNotFoundError: If plugin doesn't exist
        """
        if plugin_name not in self.plugins:
            raise PluginNotFoundError(f"Plugin '{plugin_name}' not found")

        plugin_info = self.plugins[plugin_name]
        return plugin_info.get_schemas()

    def get_plugin_port(self, plugin_name: str) -> Optional[int]:
        """Get the port number for a running plugin service"""
        return self.port_manager.get_port(plugin_name)

    def is_plugin_running(self, plugin_name: str) -> bool:
        """Check if a plugin service is currently running"""
        if plugin_name not in self.plugins:
            return False

        plugin_info = self.plugins[plugin_name]

        if plugin_info.is_remote:
            # For remote plugins, check the status we have stored
            return (plugin_name in self.plugin_statuses and
                    self.plugin_statuses[plugin_name] == PluginStatus.RUNNING)
        else:
            # For local plugins, check the actual process
            if plugin_name not in self.running_services:
                return False

            process, _ = self.running_services[plugin_name]
            is_alive = process.poll() is None

            # If process is dead, clean it up automatically (preserve logs for crash analysis)
            if not is_alive:
                logger.warning(f"Plugin '{plugin_name}' process died, cleaning up")
                self._cleanup_plugin_resources(plugin_name, preserve_logs=True, terminate_process=False)

            return is_alive

    def get_port_status(self) -> Dict:
        """Get port allocation status"""
        return self.port_manager.get_status()

    def configure_ports(self, port_ranges: List[str]) -> None:
        """
        Reconfigure port ranges (will affect new allocations only).

        Args:
            port_ranges: List of new port range strings

        Note:
            This doesn't affect already running services
        """
        self.port_manager = PortManager(port_ranges)
        logger.info(f"Port ranges reconfigured: {port_ranges}")

    def get_plugin_logs(self, plugin_name: str, lines: int = 100) -> List[str]:
        """
        Get recent logs for a plugin.

        Args:
            plugin_name: Name of the plugin
            lines: Number of recent log lines to return (default: 100)

        Returns:
            List of recent log lines

        Raises:
            PluginNotFoundError: If plugin doesn't exist
        """
        if plugin_name not in self.plugins:
            raise PluginNotFoundError(f"Plugin '{plugin_name}' not found")

        if plugin_name not in self.plugin_logs:
            return []

        return self.plugin_logs[plugin_name][-lines:]

    def clear_plugin_logs(self, plugin_name: str) -> None:
        """
        Clear stored logs for a plugin.

        Args:
            plugin_name: Name of the plugin

        Raises:
            PluginNotFoundError: If plugin doesn't exist
        """
        if plugin_name not in self.plugins:
            raise PluginNotFoundError(f"Plugin '{plugin_name}' not found")

        if plugin_name in self.plugin_logs:
            self.plugin_logs[plugin_name] = []

    def force_cleanup_plugin(self, plugin_name: str) -> None:
        """
        Force cleanup of a plugin's state, even if it appears to be running.

        Use this method when a plugin is stuck in "running" state but is not actually functioning.
        This will clean up all associated resources and forcefully kill the process if needed.

        Args:
            plugin_name: Name of the plugin to force cleanup

        Raises:
            PluginNotFoundError: If plugin doesn't exist
        """
        if plugin_name not in self.plugins:
            raise PluginNotFoundError(f"Plugin '{plugin_name}' not found")

        if plugin_name in self.running_services:
            logger.warning(f"Force cleaning up plugin '{plugin_name}'")
            # Force cleanup: preserve logs but kill process aggressively
            self._cleanup_plugin_resources(plugin_name, preserve_logs=True, terminate_process=True)
            logger.info(f"Force cleaned up plugin '{plugin_name}'")
        else:
            logger.info(f"Plugin '{plugin_name}' is not in running state, no cleanup needed")

    async def create_plugin_instance(self, plugin_name: str, config: Dict[str, Any]) -> str:
        """
        Create a new plugin instance with given configuration.

        Args:
            plugin_name: Name of the plugin
            config: Complete configuration for the plugin instance

        Returns:
            Instance ID of the created instance

        Raises:
            PluginNotFoundError: If plugin doesn't exist
            ServiceError: If plugin is not running or instance creation fails
        """
        if plugin_name not in self.plugins:
            raise PluginNotFoundError(f"Plugin '{plugin_name}' not found")

        if not self.is_plugin_running(plugin_name):
            raise ServiceError(f"Plugin '{plugin_name}' is not running")

        plugin_info = self.plugins[plugin_name]

        # Get the service URL
        if plugin_info.is_remote:
            service_url = plugin_info.base_url
        else:
            port = self.get_plugin_port(plugin_name)
            if port is None:
                raise ServiceError(f"Plugin '{plugin_name}' port not found")
            service_url = f"http://127.0.0.1:{port}"

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{service_url}/create_instance",
                    json={"config": config},
                    timeout=30.0
                )
                if response.status_code != 200:
                    raise ServiceError(
                        f"Failed to create instance for plugin '{plugin_name}': "
                        f"HTTP {response.status_code} - {response.text}"
                    )
                result = response.json()
                instance_id = result.get("instance_id")
                logger.info(f"Successfully created instance {instance_id} for plugin '{plugin_name}'")
                return instance_id
        except httpx.RequestError as e:
            raise ServiceError(f"Failed to communicate with plugin '{plugin_name}': {e}")

    async def list_plugin_instances(self, plugin_name: str) -> Dict[str, Any]:
        """
        List all instances for a plugin.

        Args:
            plugin_name: Name of the plugin

        Returns:
            Dictionary containing instance information

        Raises:
            PluginNotFoundError: If plugin doesn't exist
            ServiceError: If plugin is not running or listing fails
        """
        if plugin_name not in self.plugins:
            raise PluginNotFoundError(f"Plugin '{plugin_name}' not found")

        if not self.is_plugin_running(plugin_name):
            raise ServiceError(f"Plugin '{plugin_name}' is not running")

        plugin_info = self.plugins[plugin_name]

        # Get the service URL
        if plugin_info.is_remote:
            service_url = plugin_info.base_url
        else:
            port = self.get_plugin_port(plugin_name)
            if port is None:
                raise ServiceError(f"Plugin '{plugin_name}' port not found")
            service_url = f"http://127.0.0.1:{port}"

        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{service_url}/list_instances",
                    timeout=30.0
                )
                if response.status_code != 200:
                    raise ServiceError(
                        f"Failed to list instances for plugin '{plugin_name}': "
                        f"HTTP {response.status_code} - {response.text}"
                    )
                return response.json()
        except httpx.RequestError as e:
            raise ServiceError(f"Failed to communicate with plugin '{plugin_name}': {e}")

    async def delete_plugin_instance(self, plugin_name: str, instance_id: str) -> bool:
        """
        Delete a specific plugin instance.

        Args:
            plugin_name: Name of the plugin
            instance_id: ID of the instance to delete

        Returns:
            True if deletion was successful

        Raises:
            PluginNotFoundError: If plugin doesn't exist
            ServiceError: If plugin is not running or deletion fails
        """
        if plugin_name not in self.plugins:
            raise PluginNotFoundError(f"Plugin '{plugin_name}' not found")

        if not self.is_plugin_running(plugin_name):
            raise ServiceError(f"Plugin '{plugin_name}' is not running")

        plugin_info = self.plugins[plugin_name]

        # Get the service URL
        if plugin_info.is_remote:
            service_url = plugin_info.base_url
        else:
            port = self.get_plugin_port(plugin_name)
            if port is None:
                raise ServiceError(f"Plugin '{plugin_name}' port not found")
            service_url = f"http://127.0.0.1:{port}"

        try:
            async with httpx.AsyncClient() as client:
                response = await client.delete(
                    f"{service_url}/delete_instance/{instance_id}",
                    timeout=30.0
                )
                if response.status_code != 200:
                    raise ServiceError(
                        f"Failed to delete instance {instance_id} for plugin '{plugin_name}': "
                        f"HTTP {response.status_code} - {response.text}"
                    )
                logger.info(f"Successfully deleted instance {instance_id} for plugin '{plugin_name}'")
                return True
        except httpx.RequestError as e:
            raise ServiceError(f"Failed to communicate with plugin '{plugin_name}': {e}")
