"""
Custom exceptions for OLV Launcher.

This module defines custom exception classes that provide better error handling
and more specific error messages throughout the application.
"""


class OLVLauncherError(Exception):
    """Base exception class for all OLV Launcher errors"""

    pass


class PluginManagerNotInitializedError(OLVLauncherError):
    """Raised when plugin manager is not initialized"""

    pass


class PluginNotFoundError(OLVLauncherError):
    """Raised when a requested plugin is not found"""

    pass


class PluginAlreadyRunningError(OLVLauncherError):
    """Raised when trying to start an already running plugin"""

    pass


class PluginNotRunningError(OLVLauncherError):
    """Raised when trying to operate on a non-running plugin"""

    pass


class ServiceError(OLVLauncherError):
    """Raised when a service operation fails"""

    pass


class PortAllocationError(OLVLauncherError):
    """Raised when port allocation fails"""

    pass


class InvalidServiceTypeError(OLVLauncherError):
    """Raised when an invalid service type is provided"""

    pass


class PluginConfigurationError(OLVLauncherError):
    """Raised when plugin configuration is invalid"""

    pass
