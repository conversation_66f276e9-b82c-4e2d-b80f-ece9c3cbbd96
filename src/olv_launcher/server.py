"""
OLV Launcher FastAPI Application

Central plugin and service management for OLV platform with dependency injection
and proper FastAPI architecture.
"""

import logging
import time
from contextlib import asynccontextmanager
from typing import Optional

from fastapi import FastAPI, Depends, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from .plugin_manager import UnifiedPluginManager, ServiceType
from .routers import plugins, ports
from .dependencies import get_plugin_manager
from .middleware import LoggingMiddleware
from .exceptions import (
    PluginManagerNotInitializedError,
    PluginNotFoundError,
    ServiceError,
)
from .models import HealthResponse, ErrorResponse, PluginStatus
from .models.config import settings

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()), format=settings.log_format
)
logger = logging.getLogger(__name__)

# Global plugin manager instance
plugin_manager: Optional[UnifiedPluginManager] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifecycle events"""
    # Startup
    global plugin_manager

    try:
        plugin_manager = UnifiedPluginManager(
            plugins_dir=settings.plugins_dir, port_ranges=settings.default_port_ranges
        )

        total_plugins = len(plugin_manager.plugins)
        by_service = {
            service_type.value: len(plugin_manager.plugins_by_service[service_type])
            for service_type in ServiceType
        }

        logger.info(
            f"Launcher started - discovered {total_plugins} plugins: {by_service}"
        )

    except Exception as e:
        logger.error(f"Failed to initialize plugin manager: {e}")
        raise

    yield

    # Shutdown
    if plugin_manager:
        try:
            plugin_manager.stop_all_services()
            logger.info("All plugin services stopped successfully")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")


def create_app() -> FastAPI:
    """Create and configure FastAPI application"""

    app = FastAPI(
        title="OLV Launcher",
        description="Central plugin and service management for OLV platform",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
        lifespan=lifespan,
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=settings.cors_credentials,
        allow_methods=settings.cors_methods,
        allow_headers=settings.cors_headers,
    )

    # Add custom middleware
    app.add_middleware(LoggingMiddleware)

    # Include routers
    app.include_router(
        plugins.router,
        prefix="/plugins",
        tags=["plugins"],
        dependencies=[Depends(get_plugin_manager)],
    )



    app.include_router(
        ports.router,
        prefix="/ports",
        tags=["ports"],
        dependencies=[Depends(get_plugin_manager)],
    )

    # Global exception handlers
    @app.exception_handler(PluginManagerNotInitializedError)
    async def plugin_manager_not_initialized_handler(
        request: Request, exc: PluginManagerNotInitializedError
    ):
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=ErrorResponse(
                error="plugin_manager_not_initialized",
                message="Plugin manager not initialized",
                detail=str(exc),
            ).dict(),
        )

    @app.exception_handler(PluginNotFoundError)
    async def plugin_not_found_handler(request: Request, exc: PluginNotFoundError):
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content=ErrorResponse(
                error="plugin_not_found", message="Plugin not found", detail=str(exc)
            ).dict(),
        )

    @app.exception_handler(ServiceError)
    async def service_error_handler(request: Request, exc: ServiceError):
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=ErrorResponse(
                error="service_error",
                message="Service operation failed",
                detail=str(exc),
            ).dict(),
        )

    return app


# Create app instance
app = create_app()


@app.get(
    "/health",
    response_model=HealthResponse,
    summary="Health check",
    description="Check the health status of the launcher service",
)
async def health_check(
    manager: UnifiedPluginManager = Depends(get_plugin_manager),
) -> HealthResponse:
    """Health check endpoint for the launcher"""
    return HealthResponse(
        status=PluginStatus.RUNNING,
        service="olv-launcher",
        total_plugins=len(manager.plugins),
        running_services=len(manager.running_services),
        port_status=manager.get_port_status(),
        timestamp=int(time.time()),
    )


@app.get("/", include_in_schema=False)
async def root():
    """Root endpoint redirect to docs"""
    return {"message": "OLV Launcher API", "docs": "/docs"}


def main():
    """Main entry point for the OLV Launcher"""
    import uvicorn

    uvicorn.run(
        "src.olv_launcher.server:app",
        host=settings.host,
        port=settings.port,
        reload=settings.reload,
        log_level=settings.log_level.lower(),
    )


if __name__ == "__main__":
    main()
