# OLV Launcher UI

这是OLV（Open Loop Voice）平台的中央插件和服务管理界面，基于Next.js 15 + React + Shadcn UI + React-JSON-Schema-Form构建。

## 功能特性

- 🚀 **实时监控**: 实时显示系统健康状态和插件运行状态
- 🔧 **统一插件管理**: 支持本地和远程插件的统一管理界面
  - **本地插件**: 自动管理本地安装的插件进程和端口分配
  - **远程插件**: 连接和管理外部插件服务
- 📊 **可视化仪表板**: 直观的系统状态和插件统计信息
  - 总插件数、运行状态、本地/远程插件分布
- 📝 **日志查看**: 实时查看和下载插件日志
- ⚙️ **配置管理**: 使用React-JSON-Schema-Form的shadcn主题进行插件配置
- 🗂️ **实例管理**: 创建、查看和管理插件的多个配置实例
- 🌐 **服务URL管理**: 显示完整的服务URL，支持远程插件的外部链接访问
- 🎨 **现代UI**: 基于shadcn/ui的美观现代界面，支持插件类型可视化区分
- 🔄 **自动刷新**: 自动更新数据，保持界面信息最新

## 技术栈

- **Frontend Framework**: Next.js 15 (App Router)
- **UI Library**: Shadcn UI + Radix UI
- **Styling**: Tailwind CSS
- **Forms**: React-JSON-Schema-Form with Shadcn theme
- **Data Fetching**: SWR
- **HTTP Client**: Axios
- **Icons**: Lucide React
- **Notifications**: Sonner

## 安装和使用

### 环境要求

- Node.js 18+
- npm 或 yarn 或 pnpm

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

创建 `.env.local` 文件：

```bash
# OLV Launcher Backend API URL
NEXT_PUBLIC_API_BASE_URL=http://localhost:7000
```

### 3. 启动开发服务器

```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 4. 构建生产版本

```bash
npm run build
npm run start
```

## 项目结构

```
olv-launcher-ui/
├── app/                    # Next.js App Router页面
│   ├── layout.tsx         # 根布局
│   ├── page.tsx           # 主页
│   └── globals.css        # 全局样式
├── components/            # React组件
│   ├── ui/                # Shadcn UI组件
│   ├── plugin-card.tsx    # 插件卡片组件
│   ├── plugin-config-dialog.tsx  # 插件配置和实例管理对话框
│   ├── plugin-logs-dialog.tsx    # 插件日志对话框
│   └── plugin-status-badge.tsx   # 插件状态徽章
├── hooks/                 # 自定义Hooks
│   └── use-plugins.ts     # 插件数据管理Hook
├── lib/                   # 工具库
│   ├── api.ts             # API客户端
│   └── utils.ts           # 工具函数
└── config.example.ts      # 配置示例文件
```

## 主要功能

### 1. 系统状态监控

- 实时显示系统健康状态
- 插件统计信息：
  - **总插件数**: 系统中发现的所有插件
  - **运行状态**: 当前运行中的插件服务
  - **本地插件**: 本地管理的插件数量
  - **远程插件**: 连接的远程插件服务数量
  - **系统健康**: 整体系统运行状态
- 自动刷新数据，实时更新状态

### 2. 统一插件管理

- 按服务类型（ASR、TTS、LLM）分类显示插件
- 支持本地和远程插件的统一操作界面
- 启动和停止插件服务（本地插件）或连接检查（远程插件）
- 查看插件详细信息：
  - **基本信息**: 版本、作者、描述
  - **插件类型**: 本地/远程，带有可视化图标区分
  - **服务URL**: 完整的服务访问地址
  - **外部访问**: 远程插件支持一键打开外部链接

### 3. 实例管理 ⭐ 新功能

通过插件配置对话框，用户可以：

- **查看现有实例**: 列出插件的所有配置实例，显示实例ID、状态（Ready/Not Ready）
- **创建新实例**: 使用动态表单创建具有不同配置的插件实例
- **删除实例**: 移除不需要的插件实例
- **复制实例ID**: 便于在其他地方使用实例标识符
- **实时状态更新**: 自动刷新实例列表，显示最新状态

#### 实例管理工作流程

1. **启动插件**: 首先通过主界面启动插件服务
2. **打开配置对话框**: 点击插件卡片的配置按钮
3. **查看实例**: 在"View Instances"标签页查看现有实例
4. **创建实例**:
   - 切换到"Create Instance"标签页
   - 填写基于JSON Schema的配置表单
   - 点击"Create Instance"按钮
5. **管理实例**: 复制实例ID或删除不需要的实例

### 4. 插件配置

- 基于JSON Schema的动态表单生成
- 支持Plugin配置类型（统一了原来的Engine和Character配置）
- 使用shadcn主题的表单组件
- 表单验证和数据提交
- 重置到默认配置功能

### 5. 日志管理

- 实时查看插件日志
- 自动刷新日志内容
- 下载日志文件
- 清除日志记录
- 日志级别色彩区分

## API集成

此UI应用需要与OLV Launcher后端服务通信，确保后端服务正在运行：

```bash
python run_launcher.py
```

默认情况下，后端服务运行在 `http://localhost:7000`。

### 新增的实例管理API

- `POST /plugins/{plugin_name}/instances` - 创建插件实例
- `GET /plugins/{plugin_name}/instances` - 列出插件实例
- `DELETE /plugins/{plugin_name}/instances/{instance_id}` - 删除插件实例

## 开发指南

### 添加新组件

1. 在 `components/` 目录下创建新组件
2. 使用TypeScript进行类型定义
3. 遵循shadcn/ui的设计规范

### 添加新API

1. 在 `lib/api.ts` 中添加新的API函数
2. 定义相应的TypeScript接口
3. 在 `hooks/` 中创建对应的数据获取Hook

### 主题定制

修改 `tailwind.config.js` 和 `app/globals.css` 来自定义主题。

## 实例管理最佳实践

### 配置管理

- **命名约定**: 为不同的配置目的创建具有描述性的实例
- **配置复用**: 相同配置会生成相同的实例ID，实现自动去重
- **状态监控**: 定期检查实例状态，确保它们处于Ready状态

### 性能优化

- **按需创建**: 只在需要时创建实例，避免资源浪费
- **及时清理**: 删除不再使用的实例释放系统资源
- **批量操作**: 在进行大量实例操作时，考虑批处理

### 故障排除

- **实例未就绪**: 检查插件日志，确认配置是否正确
- **创建失败**: 验证配置表单的所有必填字段
- **删除问题**: 确认实例没有被其他服务正在使用
- **配置无法访问**: 确保插件处于运行状态后再尝试访问配置管理功能

## 故障排除

### 常见问题

1. **无法连接到后端服务**
   - 确保OLV Launcher后端服务正在运行
   - 检查 `NEXT_PUBLIC_API_BASE_URL` 环境变量设置

2. **依赖安装错误**
   - 由于使用React 19，某些包可能存在peer dependency问题
   - 使用 `--legacy-peer-deps` 标志安装

3. **shadcn组件未正确加载**
   - 确保 `components.json` 配置正确
   - 重新运行 `npx shadcn@latest init`

4. **实例管理问题**
   - 确保插件服务已启动并处于健康状态
   - 检查插件配置schema是否正确加载
   - 验证后端实例管理API的可用性

## TODO
- Bug: 生成表单中的 Slider 无法滑动，可以点击
- Feature: 能够编辑已经创建的 Instance