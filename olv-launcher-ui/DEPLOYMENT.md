# OLV Launcher UI 部署指南

本文档介绍如何部署更新后的OLV Launcher UI，支持统一的本地和远程插件管理。

## 更新内容

### 🔄 API 变更
- 移除了 `port` 字段，使用 `service_url` 统一管理服务地址
- 添加了 `is_local` 字段来区分本地和远程插件
- 更新了插件启动响应，返回完整的服务URL而不是端口号

### 🎨 UI 改进
- 新增本地/远程插件类型可视化区分（图标和标签）
- 显示完整的服务URL而不是端口号
- 远程插件支持外部链接访问
- 更新了系统统计面板，显示本地和远程插件数量
- 改进了插件卡片布局和信息展示

## 部署步骤

### 1. 环境准备

确保你有以下环境：
- Node.js 18+
- npm/yarn/pnpm
- 更新后的OLV Launcher后端服务

### 2. 安装依赖

```bash
cd olv-launcher-ui
npm install
```

### 3. 环境配置

复制环境配置文件：
```bash
cp .env.example .env.local
```

编辑 `.env.local` 文件：
```bash
# 确保指向正确的后端API地址
NEXT_PUBLIC_API_BASE_URL=http://localhost:7000
```

### 4. 开发环境启动

```bash
npm run dev
```

访问 http://localhost:3000 查看应用。

### 5. 生产环境部署

#### 构建应用
```bash
npm run build
```

#### 启动生产服务器
```bash
npm start
```

#### 使用PM2部署（推荐）
```bash
# 安装PM2
npm install -g pm2

# 启动应用
pm2 start npm --name "olv-launcher-ui" -- start

# 保存PM2配置
pm2 save
pm2 startup
```

## 兼容性说明

### 后端兼容性
- ✅ 兼容更新后的OLV Launcher后端（支持service_url和is_local字段）
- ❌ 不兼容旧版本后端（仍使用port字段的版本）

### 浏览器兼容性
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 功能验证

部署完成后，请验证以下功能：

### 1. 基本功能
- [ ] 系统状态面板正常显示
- [ ] 插件列表正确加载
- [ ] 本地/远程插件类型正确显示

### 2. 本地插件
- [ ] 可以启动和停止本地插件
- [ ] 显示正确的服务URL（如 http://127.0.0.1:8001）
- [ ] 插件状态实时更新

### 3. 远程插件
- [ ] 正确显示远程插件信息
- [ ] 外部链接按钮可以打开远程服务
- [ ] 状态检查正常工作

### 4. 统计信息
- [ ] 总插件数正确
- [ ] 运行中插件数正确
- [ ] 本地插件数正确
- [ ] 远程插件数正确

## 故障排除

### 常见问题

1. **插件类型显示错误**
   - 检查后端是否返回了 `is_local` 字段
   - 确认后端版本是否为最新

2. **服务URL显示为空**
   - 检查后端是否返回了 `service_url` 字段
   - 确认插件配置是否正确

3. **外部链接无法打开**
   - 检查远程插件的 `service_url` 是否可访问
   - 确认浏览器没有阻止弹出窗口

4. **统计数据不准确**
   - 刷新页面重新加载数据
   - 检查后端API响应是否正确

### 调试模式

启用调试模式：
```bash
NODE_ENV=development npm run dev
```

查看浏览器控制台获取详细错误信息。

## 性能优化

### 生产环境优化
- 启用gzip压缩
- 配置CDN加速
- 设置适当的缓存策略

### 监控建议
- 使用PM2监控应用状态
- 配置日志轮转
- 设置健康检查端点

## 更新日志

### v2.0.0 (当前版本)
- 🔄 重构插件类型系统，支持统一的本地/远程插件管理
- 🎨 更新UI设计，改进插件信息展示
- 🌐 添加服务URL管理和外部链接支持
- 📊 增强系统统计面板

### v1.x.x (旧版本)
- 基于端口号的插件管理
- 仅支持本地插件
- 基础的插件操作界面

## 支持

如遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查浏览器控制台错误信息
3. 确认后端服务版本兼容性
4. 提交Issue到项目仓库
