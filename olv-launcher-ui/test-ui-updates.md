# UI 更新测试清单

## 已完成的更新

### ✅ 插件卡片更新
- [x] 所有插件都显示URL字段（包括未运行的插件）
- [x] 未运行插件显示 "None" 作为URL
- [x] 远程插件的URL支持外部链接（仅当URL存在时）
- [x] 本地/远程插件类型可视化区分（图标和标签）

### ✅ 主页面更新
- [x] 删除了 System Health 卡片
- [x] 调整网格布局为4列（Total Plugins, Running Services, Local Plugins, Remote Plugins）
- [x] 删除了不再使用的导入和函数
- [x] 保留了header中的系统状态指示器

### ✅ API类型更新
- [x] 更新 PluginInfo 接口，使用 service_url 和 is_local
- [x] 更新 PluginStartResponse 接口，返回 service_url
- [x] 更新 PluginHealthResponse 接口，使用 service_url
- [x] 更新启动成功消息，显示完整URL

## 测试场景

### 1. 本地插件测试
- [ ] 启动本地插件，检查是否显示正确的service_url（如 http://127.0.0.1:8001）
- [ ] 停止本地插件，检查URL是否显示为 "None"
- [ ] 验证本地插件图标显示为Monitor图标（蓝色）
- [ ] 验证本地插件类型标签显示为 "Local"

### 2. 远程插件测试
- [ ] 检查远程插件是否显示正确的service_url
- [ ] 验证远程插件的外部链接按钮是否可点击
- [ ] 验证远程插件图标显示为Cloud图标（紫色）
- [ ] 验证远程插件类型标签显示为 "Remote"

### 3. 统计面板测试
- [ ] 验证 "Total Plugins" 显示正确的总数
- [ ] 验证 "Running Services" 显示正确的运行中插件数
- [ ] 验证 "Local Plugins" 显示正确的本地插件数（蓝色）
- [ ] 验证 "Remote Plugins" 显示正确的远程插件数（紫色）
- [ ] 确认没有 "System Health" 卡片

### 4. 响应式布局测试
- [ ] 在桌面端验证4列布局正常显示
- [ ] 在平板端验证2列布局正常显示
- [ ] 在移动端验证单列布局正常显示

### 5. 交互功能测试
- [ ] 点击远程插件的外部链接按钮，验证是否在新标签页打开
- [ ] 验证本地插件不显示外部链接按钮
- [ ] 验证插件启动/停止功能正常工作
- [ ] 验证toast消息显示正确的service_url

## 预期行为

### URL显示逻辑
```
- 插件运行中 + 有service_url → 显示完整URL
- 插件运行中 + 无service_url → 显示 "None"
- 插件未运行 → 显示 "None"
```

### 外部链接显示逻辑
```
- 远程插件 + 有service_url → 显示外部链接按钮
- 远程插件 + 无service_url → 不显示外部链接按钮
- 本地插件 → 不显示外部链接按钮
```

### 插件类型显示
```
- is_local: true → Monitor图标(蓝色) + "Local"标签
- is_local: false → Cloud图标(紫色) + "Remote"标签
```

## 兼容性说明

### 后端要求
- 必须使用更新后的OLV Launcher后端
- API响应必须包含 `service_url` 和 `is_local` 字段
- 不再支持仅返回 `port` 字段的旧版本后端

### 浏览器兼容性
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 故障排除

### 常见问题
1. **URL显示为undefined**
   - 检查后端是否返回了service_url字段
   - 确认API响应格式正确

2. **插件类型显示错误**
   - 检查后端是否返回了is_local字段
   - 确认字段值为boolean类型

3. **外部链接无法点击**
   - 检查service_url是否为有效URL
   - 确认浏览器没有阻止弹出窗口

4. **统计数据不正确**
   - 刷新页面重新加载数据
   - 检查后端API响应是否包含所有必要字段
