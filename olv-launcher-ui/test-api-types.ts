/**
 * Test file to verify API type definitions work correctly
 * This file can be run with `npx tsx test-api-types.ts` to verify types
 */

import { PluginInfo, PluginStartResponse, PluginHealthResponse, PluginStatus } from './lib/api';

// Test the updated PluginInfo interface
const testLocalPlugin: PluginInfo = {
  name: 'test-local-asr',
  service_type: 'asr',
  status: PluginStatus.RUNNING,
  service_url: 'http://127.0.0.1:8001',
  is_local: true,
  description: 'Test local ASR plugin',
  version: '1.0.0',
  author: 'Test Author'
};

const testRemotePlugin: PluginInfo = {
  name: 'test-remote-asr',
  service_type: 'asr',
  status: PluginStatus.RUNNING,
  service_url: 'https://api.example.com/asr',
  is_local: false,
  description: 'Test remote ASR plugin',
  version: '2.0.0',
  author: 'Remote Provider'
};

// Test the updated PluginStartResponse interface
const testStartResponse: PluginStartResponse = {
  message: 'Plugin started successfully',
  plugin_name: 'test-plugin',
  service_url: 'http://127.0.0.1:8001',
  status: PluginStatus.RUNNING
};

// Test the updated PluginHealthResponse interface
const testHealthResponse: PluginHealthResponse = {
  plugin_name: 'test-plugin',
  status: PluginStatus.RUNNING,
  service_url: 'http://127.0.0.1:8001',
  http_status: 200,
  message: 'Plugin is healthy'
};

// Test plugin type checking functions
function isLocalPlugin(plugin: PluginInfo): boolean {
  return plugin.is_local;
}

function getPluginServiceUrl(plugin: PluginInfo): string | undefined {
  return plugin.service_url;
}

function getPluginTypeLabel(plugin: PluginInfo): string {
  return plugin.is_local ? 'Local' : 'Remote';
}

// Test the functions
console.log('Testing API types...');

console.log('Local plugin test:', {
  name: testLocalPlugin.name,
  isLocal: isLocalPlugin(testLocalPlugin),
  serviceUrl: getPluginServiceUrl(testLocalPlugin),
  typeLabel: getPluginTypeLabel(testLocalPlugin)
});

console.log('Remote plugin test:', {
  name: testRemotePlugin.name,
  isLocal: isLocalPlugin(testRemotePlugin),
  serviceUrl: getPluginServiceUrl(testRemotePlugin),
  typeLabel: getPluginTypeLabel(testRemotePlugin)
});

console.log('Start response test:', testStartResponse);
console.log('Health response test:', testHealthResponse);

console.log('✅ All API types are working correctly!');

export {
  testLocalPlugin,
  testRemotePlugin,
  testStartResponse,
  testHealthResponse,
  isLocalPlugin,
  getPluginServiceUrl,
  getPluginTypeLabel
};
