/* eslint-disable @typescript-eslint/no-unused-vars */
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { PluginStatusBadge } from './plugin-status-badge';
import { PluginInfo, PluginStatus } from '@/lib/api';
import { usePluginActions } from '@/hooks/use-plugins';
import { Play, Square, Settings, Terminal } from 'lucide-react';
import { PluginConfigDialog } from '@/components/plugin-config-dialog';
import { PluginLogsDialog } from '@/components/plugin-logs-dialog';

interface PluginCardProps {
  plugin: PluginInfo;
  onRefresh?: () => void;
}

export function PluginCard({ plugin, onRefresh }: PluginCardProps) {
  const { startPlugin, stopPlugin, loading } = usePluginActions();
  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  const [logsDialogOpen, setLogsDialogOpen] = useState(false);

  const isRunning = plugin.status === PluginStatus.RUNNING;
  const isLoading = loading[plugin.name];
  
  // Use plugin status directly
  const displayStatus = plugin.status;

  const handleStart = async () => {
    try {
      await startPlugin(plugin.name);
      onRefresh?.();
    } catch (error) {
      // Error is handled in the hook
    }
  };

  const handleStop = async () => {
    try {
      await stopPlugin(plugin.name);
      onRefresh?.();
    } catch (error) {
      // Error is handled in the hook
    }
  };

  const getServiceTypeColor = (serviceType: string) => {
    switch (serviceType.toLowerCase()) {
      case 'asr':
        return 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800';
      case 'tts':
        return 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-800';
      case 'llm':
        return 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800';
    }
  };

  return (
    <>
      <Card className="transition-all duration-200 hover:shadow-md flex flex-col">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              <CardTitle className="text-lg font-semibold">{plugin.name}</CardTitle>
              <CardDescription className="text-sm text-muted-foreground">
                {plugin.description || 'No description available'}
              </CardDescription>
            </div>
            <div className="flex flex-col gap-2 items-end">
              <Badge className={getServiceTypeColor(plugin.service_type)}>
                {plugin.service_type.toUpperCase()}
              </Badge>
              <PluginStatusBadge status={displayStatus} />
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-0 flex-1 flex flex-col justify-end">
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            {plugin.version && (
              <div className="flex items-center gap-1">
                <span>Version:</span>
                <code className="text-xs bg-muted px-1 py-0.5 rounded">{plugin.version}</code>
              </div>
            )}
            {plugin.port && (
              <div className="flex items-center gap-1">
                <span>Port:</span>
                <code className="text-xs bg-muted px-1 py-0.5 rounded">{plugin.port}</code>
              </div>
            )}
          </div>
        </CardContent>

        <CardFooter className="pt-3">
          <div className="flex gap-2 w-full">
            {/* Start/Stop button */}
            {isRunning ? (
              <Button
                variant="outline"
                size="sm"
                onClick={handleStop}
                disabled={isLoading}
                className="flex items-center gap-1"
              >
                <Square size={14} />
                {isLoading ? 'Stopping...' : 'Stop'}
              </Button>
            ) : (
              <Button
                variant="default"
                size="sm"
                onClick={handleStart}
                disabled={isLoading}
                className="flex items-center gap-1"
              >
                <Play size={14} />
                {isLoading ? 'Starting...' : 'Start'}
              </Button>
            )}

            {/* Configuration button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setConfigDialogOpen(true)}
              disabled={!isRunning}
              className="flex items-center gap-1"
              title={!isRunning ? "Plugin must be running to access configuration" : "Configure plugin"}
            >
              <Settings size={14} />
              Config
            </Button>

            {/* Logs button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setLogsDialogOpen(true)}
              className="flex items-center gap-1"
            >
              <Terminal size={14} />
              Logs
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* Configuration Dialog */}
      <PluginConfigDialog
        open={configDialogOpen}
        onOpenChange={setConfigDialogOpen}
        pluginName={plugin.name}
        pluginStatus={plugin.status}
      />

      {/* Logs Dialog */}
      <PluginLogsDialog
        open={logsDialogOpen}
        onOpenChange={setLogsDialogOpen}
        pluginName={plugin.name}
      />
    </>
  );
} 