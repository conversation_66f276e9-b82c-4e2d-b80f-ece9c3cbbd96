import { Badge } from '@/components/ui/badge';
import { CheckCircle, AlertCircle, XCircle, Loader2, Square } from 'lucide-react';
import { PluginStatus } from '@/lib/api';

interface PluginStatusBadgeProps {
  status: PluginStatus;
  size?: 'sm' | 'lg';
}

export function PluginStatusBadge({ status, size = 'sm' }: PluginStatusBadgeProps) {
  const getStatusConfig = () => {
    switch (status) {
      case PluginStatus.RUNNING:
        return {
          variant: 'default' as const,
          icon: CheckCircle,
          className: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800',
          text: 'Running',
        };
      case PluginStatus.STOPPED:
        return {
          variant: 'secondary' as const,
          icon: Square,
          className: 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800',
          text: 'Stopped',
        };
      case PluginStatus.STARTING:
        return {
          variant: 'outline' as const,
          icon: Loader2,
          className: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800',
          text: 'Starting',
        };
      case PluginStatus.STOPPING:
        return {
          variant: 'outline' as const,
          icon: Loader2,
          className: 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800',
          text: 'Stopping',
        };
      case PluginStatus.ERROR:
        return {
          variant: 'destructive' as const,
          icon: XCircle,
          className: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800',
          text: 'Error',
        };
      default:
        return {
          variant: 'outline' as const,
          icon: AlertCircle,
          className: 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800',
          text: 'Unknown',
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;
  const iconSize = size === 'sm' ? 12 : 16;

  return (
    <Badge variant={config.variant} className={`${config.className} flex items-center gap-1`}>
      <Icon 
        size={iconSize} 
        className={status === PluginStatus.STARTING || status === PluginStatus.STOPPING ? 'animate-spin' : ''} 
      />
      <span>{config.text}</span>
    </Badge>
  );
} 