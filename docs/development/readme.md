# OLV Launcher Development Guide

This guide covers the development aspects of the OLV Launcher plugin system, including architecture details, development workflows, and best practices.

## Architecture Overview

### Core Components

1. **UnifiedPluginManager** (`src/olv_launcher/plugin_manager.py`)
   - Central plugin management system
   - Handles both local and remote plugin discovery
   - Manages plugin lifecycle and health monitoring
   - Coordinates with PortManager for local plugins

2. **Plugin Models** (`src/olv_launcher/models/plugin.py`)
   - `PluginType`: Enum for local/remote plugin types
   - `PluginConfig`: Configuration model with validation
   - `PluginInfo`: Runtime plugin information

3. **API Layer** (`src/olv_launcher/routers/plugins.py`)
   - RESTful API endpoints for plugin management
   - Unified interface for local and remote plugins
   - Instance management and health checking

4. **ASR Server** (`plugins/asr/asr_server.py`)
   - Generic FastAPI server for ASR plugins
   - Plugin configuration exposure endpoint
   - Instance management capabilities

### Plugin Discovery Flow

#### Local Plugin Discovery
1. Scan `plugins_dir` for service type directories (asr, tts, llm)
2. For each plugin directory, check for `plugin.json` and `pyproject.toml`
3. Validate plugin configuration and service type
4. Sync plugin environment using UV (if enabled)
5. Register plugin in the manager

#### Remote Plugin Discovery
1. Iterate through configured `remote_plugins` URLs
2. Make HTTP request to `{base_url}/plugin-config`
3. Validate returned configuration
4. Register remote plugin with base_url

### Plugin Lifecycle Management

#### Local Plugins
- **Start**: Allocate port → Launch UV process → Wait for health check → Mark as running
- **Stop**: Terminate process → Release port → Clean up resources → Mark as stopped
- **Health**: Check process status and HTTP health endpoint

#### Remote Plugins
- **Start**: Check HTTP health endpoint → Mark as running
- **Stop**: Mark as stopped (no process to terminate)
- **Health**: Check HTTP health endpoint

## Development Workflow

### Setting Up Development Environment

```bash
# Clone repository
git clone <repository-url>
cd OLV

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install in development mode
pip install -e .

# Install development dependencies
pip install pytest pytest-asyncio httpx

# Set up pre-commit hooks (optional)
pip install pre-commit
pre-commit install
```

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src/olv_launcher

# Run specific test file
pytest tests/test_plugin_manager.py

# Run async tests
pytest -v tests/test_async_operations.py
```

### Development Server

```bash
# Start development server with auto-reload
python run_launcher.py --reload

# Or with custom configuration
OLV_LAUNCHER_LOG_LEVEL=DEBUG python run_launcher.py
```

## Plugin Development

### Local Plugin Development

#### 1. Plugin Structure
```
plugins/asr/my_plugin/
├── plugin.json          # Plugin metadata and configuration
├── pyproject.toml       # Python project configuration
├── src/
│   └── my_plugin/
│       ├── __init__.py
│       ├── engine.py    # ASR engine implementation
│       └── server.py    # FastAPI server entry point
└── tests/               # Plugin tests (optional)
```

#### 2. Engine Implementation
```python
# src/my_plugin/engine.py
from plugins.asr.asr_engine_interface import ASREngineInterface
import numpy as np
from typing import Dict, Any, Optional

class MyASREngine(ASREngineInterface):
    def __init__(self):
        super().__init__()
        self.model = None

    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the ASR engine with configuration"""
        self._config = config
        # Load model, initialize resources
        self.model = self._load_model(config["model_path"])
        self._is_initialized = True

    async def transcribe(self, audio: np.ndarray, custom: Optional[Dict[str, Any]] = None) -> str:
        """Transcribe audio to text"""
        if not self.is_ready():
            raise RuntimeError("Engine not initialized")

        # Validate audio format
        self.validate_audio(audio)

        # Perform transcription
        result = self.model.transcribe(audio)
        return result

    def is_ready(self) -> bool:
        """Check if engine is ready for transcription"""
        return self._is_initialized and self.model is not None

    async def cleanup(self) -> None:
        """Clean up resources"""
        if self.model:
            self.model.cleanup()
        self._is_initialized = False

    def _load_model(self, model_path: str):
        # Implement model loading logic
        pass
```

#### 3. Server Configuration
```python
# src/my_plugin/server.py
import os

# Set environment variables for the generic ASR server
os.environ["ASR_ENGINE_MODULE"] = "my_plugin.engine"
os.environ["ASR_ENGINE_CLASS"] = "MyASREngine"

# Import the generic ASR server
from plugins.asr.asr_server import app

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("my_plugin.server:app", host="0.0.0.0", port=8000, reload=False)
```

### Remote Plugin Development

#### 1. FastAPI Server Implementation
```python
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Dict, Any
import uuid

app = FastAPI(title="My Remote ASR Plugin")

# Global state
instances = {}
plugin_config = {
    "name": "my_remote_asr",
    "version": "1.0.0",
    "description": "My Remote ASR Plugin",
    "author": "Developer",
    "service_type": "asr",
    "plugin_type": "remote",
    "base_url": "http://localhost:8080",
    "plugin_json_schema": {
        "type": "object",
        "properties": {
            "model_name": {"type": "string", "enum": ["small", "medium", "large"]},
            "language": {"type": "string", "default": "auto"}
        },
        "required": ["model_name"]
    }
}

class InstanceConfig(BaseModel):
    config: Dict[str, Any]

class TranscribeRequest(BaseModel):
    audio_data: str  # Base64 encoded audio
    instance_id: str

@app.get("/health")
async def health():
    return {"status": "healthy", "service": "my_remote_asr"}

@app.get("/plugin-config")
async def get_plugin_config():
    return plugin_config

@app.post("/create_instance")
async def create_instance(request: InstanceConfig):
    instance_id = str(uuid.uuid4())
    instances[instance_id] = {
        "config": request.config,
        "created_at": "2024-01-01T00:00:00Z",
        "status": "ready"
    }
    return {"instance_id": instance_id}

@app.get("/list_instances")
async def list_instances():
    return instances

@app.delete("/delete_instance/{instance_id}")
async def delete_instance(instance_id: str):
    if instance_id not in instances:
        raise HTTPException(status_code=404, detail="Instance not found")
    del instances[instance_id]
    return {"message": "Instance deleted"}

@app.post("/transcribe")
async def transcribe(request: TranscribeRequest):
    if request.instance_id not in instances:
        raise HTTPException(status_code=404, detail="Instance not found")

    # Implement transcription logic
    # Decode base64 audio, process, return text
    return {"text": "transcribed text"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8080)
```

## Testing

### Unit Tests

```python
# tests/test_plugin_manager.py
import pytest
from src.olv_launcher.plugin_manager import UnifiedPluginManager
from src.olv_launcher.models.plugin import PluginType

@pytest.fixture
def plugin_manager():
    return UnifiedPluginManager(plugins_dir="test_plugins")

def test_local_plugin_discovery(plugin_manager):
    # Test local plugin discovery
    plugin_manager.discover_plugins()
    assert len(plugin_manager.plugins) > 0

@pytest.mark.asyncio
async def test_remote_plugin_discovery(plugin_manager):
    # Mock remote plugin
    with patch('httpx.AsyncClient.get') as mock_get:
        mock_get.return_value.status_code = 200
        mock_get.return_value.json.return_value = {
            "name": "test_remote",
            "service_type": "asr",
            "plugin_type": "remote"
        }

        await plugin_manager.discover_remote_plugins()
        assert "test_remote" in plugin_manager.plugins
```

### Integration Tests

```python
# tests/test_integration.py
import pytest
import httpx
from fastapi.testclient import TestClient
from src.olv_launcher.server import app

client = TestClient(app)

def test_plugin_list_endpoint():
    response = client.get("/plugins/")
    assert response.status_code == 200
    data = response.json()
    assert "plugins" in data

def test_plugin_start_stop_cycle():
    # Test starting a plugin
    response = client.post("/plugins/test_plugin/start")
    assert response.status_code == 200

    # Test stopping the plugin
    response = client.post("/plugins/test_plugin/stop")
    assert response.status_code == 200
```

## Best Practices

### Plugin Development

1. **Error Handling**: Always implement proper error handling and cleanup
2. **Resource Management**: Use async context managers for resource cleanup
3. **Configuration Validation**: Validate all configuration parameters
4. **Logging**: Use structured logging for debugging
5. **Testing**: Write comprehensive tests for your plugin

### Performance Considerations

1. **Async Operations**: Use async/await for I/O operations
2. **Connection Pooling**: Reuse HTTP connections for remote plugins
3. **Caching**: Cache plugin configurations and schemas
4. **Timeouts**: Set appropriate timeouts for all network operations

### Security

1. **Input Validation**: Validate all inputs, especially from remote sources
2. **Authentication**: Implement authentication for remote plugins if needed
3. **HTTPS**: Use HTTPS for remote plugin communications in production
4. **Sandboxing**: Consider sandboxing local plugins for security

## Debugging

### Common Issues

1. **Plugin Not Starting**:
   - Check plugin logs: `GET /plugins/{name}/logs`
   - Verify configuration: `GET /plugins/{name}/schemas`
   - Check port availability

2. **Remote Plugin Connection Issues**:
   - Verify base_url accessibility
   - Check network connectivity
   - Increase timeout settings

3. **Instance Creation Failures**:
   - Validate configuration against schema
   - Check plugin health status
   - Review plugin-specific logs

### Debug Tools

```python
# Enable debug logging
import logging
logging.getLogger("src.olv_launcher").setLevel(logging.DEBUG)

# Use httpx for manual testing
import httpx
async with httpx.AsyncClient() as client:
    response = await client.get("http://localhost:7000/plugins/")
    print(response.json())
```

## Contributing

### Code Style

- Follow PEP 8 for Python code style
- Use type hints for all function signatures
- Write docstrings for all public methods
- Use meaningful variable and function names

### Pull Request Process

1. Create feature branch from main
2. Implement changes with tests
3. Update documentation
4. Run full test suite
5. Submit pull request with clear description

### Release Process

1. Update version numbers
2. Update CHANGELOG.md
3. Create release tag
4. Build and publish packages
5. Update documentation