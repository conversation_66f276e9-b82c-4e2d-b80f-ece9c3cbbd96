# API 结构

## ASR Plugin 端点

ASR Plugin 现在有两个端点：

### 1. POST `/transcribe`
转录音频数据，使用已存在的插件配置实例。

**请求体：**
```json
{
    "audio": "base64_encoded_audio_data",
    "plugin_config": {
        "model_type": "sense_voice",
        "sense_voice": "./models/model.onnx",
        "tokens": "./models/tokens.txt",
        "provider": "cpu",
        "num_threads": 4,
        "sample_rate": 16000,
        "debug": false,
        "use_itn": true,
        "whisper_language": "zh",
        "whisper_task": "transcribe"
    }
}
```

**响应：**
```json
{
    "text": "转录的文本",
    "processing_time": 0.123,
    "instance_id": "abc123def456"
}
```

**错误处理：**
- 如果 `plugin_config` 对应的实例不存在，返回 404 错误
- 不会自动创建新实例

### 2. GET `/health`
检查 ASR 服务健康状态。

**响应：**
```json
{
    "status": "healthy"  // healthy, partial, not_ready, no_instances
}
```

## 使用流程

### 1. 启动插件
```bash
curl -X POST http://127.0.0.1:8000/plugins/sherpa_onnx_asr_cpu_plugin/start
```

### 2. 创建实例


### 3. 转录音频


## 错误处理

### 常见错误

1. **404 - 实例不存在**
   ```json
   {
     "detail": "No engine instance found for the provided plugin_config. Instance ID: abc123def456"
   }
   ```

2. **400 - 缺少必需字段**
   ```json
   {
     "detail": "audio field is required"
   }
   ```

3. **503 - 实例未就绪**
   ```json
   {
     "detail": "ASR engine instance not ready"
   }
   ```