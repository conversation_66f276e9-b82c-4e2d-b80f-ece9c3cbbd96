# 数据模型 (Models)

OLV Launcher 使用 Pydantic模型 来定义其数据结构，确保数据的一致性和有效性。这些模型广泛应用于 API 请求/响应、配置管理以及内部数据表示。

## 核心模型

核心模型定义了 Launcher 的基本构建块，如插件、服务和端口。

### `PluginInfo` (`plugin.py`)

`PluginInfo` 模型封装了有关已发现插件的元数据。

| 字段名           | 类型                         | 描述                                                                 |
|----------------|------------------------------|----------------------------------------------------------------------|
| `name`           | `str`                        | 插件的唯一名称。                                                           |
| `config_path`    | `Path`                       | 指向插件 `plugin.json` 配置文件的路径。                                       |
| `config`         | `PluginConfig`               | 一个 `PluginConfig` 对象 (详见下方 `PluginConfig` 部分)，包含插件的具体配置。        |
| `plugin_dir`     | `Path` (computed)            | 插件的根目录路径。                                                          |
| `version`        | `str` (computed)             | 从 `PluginConfig` 获取的插件版本。                                          |
| `description`    | `str` (computed)             | 从 `PluginConfig` 获取的插件描述。                                          |
| `author`         | `str` (computed)             | 从 `PluginConfig` 获取的插件作者。                                          |
| `service_type`   | `ServiceType` (computed)     | 从 `PluginConfig` 获取并转换为 `ServiceType` 枚举的服务类型。                     |

`PluginInfo` 类还提供了 `from_config_file` 方法，用于从 `plugin.json` 文件加载和验证插件配置，并创建 `PluginInfo` 实例。`get_schema_paths` 方法返回插件的 JSON schema 和 UI schema 文件的绝对路径。


### `PluginConfig` (`plugin.py`)

`PluginConfig` 模型定义了插件配置文件的结构，通常存储在每个插件目录下的 `plugin.json` 文件中。

| 字段名         | 类型                | 描述                                                                                           |
|--------------|---------------------|----------------------------------------------------------------------------------------------|
| `version`      | `str`               | 插件版本字符串。                                                                                 |
| `service_type` | `str`               | 插件提供的服务类型字符串。                                                                         |
| `description`  | `str`               | 插件的可选描述。                                                                                 |
| `author`       | `str`               | 插件的可选作者。                                                                                 |
| `schemas`      | `Dict[str, str]`    | 一个字典，定义了 JSON schema 和 UI schema 文件的相对路径，例如 `{"plugin_json_schema": "schemas/plugin_json_schema.json", "plugin_ui_schema": "schemas/plugin_ui_schema.json"}`。 |

### `ServiceType` (`service.py`)

`ServiceType` 是一个枚举类型，定义了 Launcher 支持的标准服务类型。

| 枚举值 | 描述                                     |
|------|------------------------------------------|
| `ASR`  | 自动语音识别 (Automatic Speech Recognition) |
| `TTS`  | 文本转语音 (Text-to-Speech)                |
| `LLM`  | 大型语言模型 (Large Language Model)        |

### `PortRange` (`port.py`)

`PortRange` 模型用于定义和管理端口号范围。

| 字段名   | 类型  | 描述             |
|--------|-------|------------------|
| `start`  | `int` | 范围的起始端口号。     |
| `end`    | `int` | 范围的结束端口号。     |

该模型包含验证逻辑，确保 `start` 端口不大于 `end` 端口。它还提供了辅助方法，如检查特定端口是否在范围内 (`__contains__`) 和将范围转换为端口列表 (`to_list`)。

## API 模型 (`api.py`)

API 模型定义了 Launcher REST API 的请求和响应体结构。这些模型确保了客户端和服务器之间的数据交换是类型安全和经过验证的。

关于这些模型的详细定义和用法，请参阅 [API 参考 (API Reference)](./api-reference.md)。

## 配置模型 (`config.py`)

配置模型 (`LauncherSettings`) 用于管理 OLV Launcher 的各项配置参数。这些设置可以通过 `.env` 文件进行配置。

关于配置选项的详细说明和可用参数，请参阅 [配置管理 (Configuration)](./configuration.md)。

