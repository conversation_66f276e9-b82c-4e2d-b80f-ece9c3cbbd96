# 插件管理器 (Plugin Manager)

OLV Launcher 的插件管理器 (`UnifiedPluginManager`) 负责统一管理所有服务类型（ASR, TTS, LLM）的插件。它处理插件的发现、环境同步、生命周期控制、端口分配和日志记录。

## 核心职责

`UnifiedPluginManager` 的主要职责包括：

*   **插件发现**: 扫描指定目录（通常由 `settings.plugins_dir` 配置，默认为 `"plugins/"`），根据 `plugin.json` 和目录结构识别合法的插件。
*   **环境同步**: 若启用 (`settings.enable_uv_sync`)，自动使用 `uv sync` 命令同步插件的 Python 环境，确保依赖正确安装。同步超时由 `settings.uv_sync_timeout` 控制。
*   **生命周期管理**: 启动和停止插件服务进程。
*   **端口管理**: 与端口管理器 (`PortManager`) 协作，为插件动态分配和释放运行所需的端口。
*   **日志收集**: 捕获插件进程的标准输出 (stdout) 和标准错误 (stderr) 流，并提供查阅接口。
*   **状态查询**: 提供查询插件运行状态、所用端口、已发现插件列表等信息的功能。
*   **Schema 管理**: 从插件配置中获取 JSON Schema 和 UI Schema，支持实例创建的表单配置。

## 插件配置结构 (plugin.json)

新的 `plugin.json` 结构直接包含所有配置信息，包括 JSON Schema 和 UI Schema：

```json
{
  "name": "plugin_name",
  "version": "1.0.0", 
  "description": "Plugin description",
  "author": "Author Name",
  "service_type": "asr",
  "package_manager": "uv",
  "plugin_json_schema": {
    "type": "object",
    "title": "Plugin Configuration",
    "properties": {
      "model_path": {
        "type": "string",
        "title": "Model Path"
      }
    },
    "required": ["model_path"],
    "default": {
      "model_path": "./models/default_model.onnx"
    }
  },
  "plugin_ui_schema": {
    "ui:title": "Plugin Configuration",
    "model_path": {
      "ui:widget": "textarea",
      "ui:placeholder": "Enter model path..."
    }
  }
}
```

### 关键字段说明

| 字段 | 类型 | 必需 | 描述 |
|------|------|------|------|
| `name` | string | 是 | 插件名称，必须与目录名一致 |
| `version` | string | 是 | 插件版本号 |
| `service_type` | string | 是 | 服务类型 (asr, tts, llm) |
| `plugin_json_schema` | object | 否 | JSON Schema 定义，包含配置属性和默认值 |
| `plugin_ui_schema` | object | 否 | UI Schema 定义，控制表单渲染 |

### JSON Schema 特性

- **default 字段**: `plugin_json_schema.default` 包含创建实例时的默认配置
- **validation**: 支持完整的 JSON Schema 验证规则
- **类型支持**: string, integer, number, boolean, object, array

### UI Schema 特性

- **widget 控制**: 指定表单控件类型 (textarea, select, checkbox 等)
- **布局控制**: 控制字段显示顺序和分组
- **提示信息**: placeholder, description, help 等用户友好信息

## 初始化

`UnifiedPluginManager` 在应用程序启动时被实例化。其关键配置参数主要通过构造函数传入，这些值通常来源于 `LauncherSettings` (详见 [配置管理](./configuration.md))。

| 参数          | 描述                                             | 来源                                                              | 示例值 (来自 `settings`)                                  |
|---------------|--------------------------------------------------|-------------------------------------------------------------------|-----------------------------------------------------------|
| `plugins_dir` | 存放插件的根目录路径字符串。                                 | 构造函数参数 (通常由 `settings.plugins_dir` 提供)                     | `"plugins"`                                               |
| `port_ranges` | 用于插件端口分配的端口范围字符串列表。                           | 构造函数参数 (通常由 `settings.default_port_ranges` 提供)             | `["8001-8020", "9001-9020", "10001-10020"]`               |

## 工作流程

```mermaid
flowchart TD
    A[启动 UnifiedPluginManager] --> B[扫描 plugins_dir]
    B --> C[遍历服务类型目录]
    C --> D[检查插件目录]
    D --> E{包含 plugin.json 
    和 pyproject.toml?}
    E -->|否| F[跳过该目录]
    E -->|是| G[解析 plugin.json]
    G --> H{service_type 匹配目录?}
    H -->|否| I[记录警告]
    H -->|是| J{启用 UV 同步?}
    J -->|是| K[执行 uv sync]
    J -->|否| L[存储插件信息和Schema]
    K --> M{同步成功?}
    M -->|是| L
    M -->|否| N[记录错误但继续]
    N --> L
    F --> O{还有目录?}
    I --> O
    L --> O
    O -->|是| D
    O -->|否| P[插件发现完成]
    
    Q[启动插件请求] --> R{插件存在?}
    R -->|否| S[返回错误]
    R -->|是| T{插件已运行?}
    T -->|是| U[返回现有端口]
    T -->|否| V[分配端口]
    V --> W{端口分配成功?}
    W -->|否| X[抛出 ServiceError]
    W -->|是| Y[构建启动命令]
    Y --> Z[启动进程]
    Z --> AA[启动日志捕获线程]
    AA --> BB[健康检查]
    BB --> CC{服务就绪?}
    CC -->|否| DD{超时?}
    DD -->|是| EE[清理资源并抛出错误]
    DD -->|否| BB
    CC -->|是| FF[记录运行状态]
    
    GG[停止插件请求] --> HH{插件运行中?}
    HH -->|否| II[返回成功]
    HH -->|是| JJ[发送 terminate 信号]
    JJ --> KK[等待 10 秒]
    KK --> LL{进程已退出?}
    LL -->|否| MM[发送 kill 信号]
    LL -->|是| NN[释放端口]
    MM --> NN
    NN --> OO[清理状态记录]
    OO --> PP[停止完成]
```

### 1. 插件发现 (`discover_plugins`)

- 管理器启动时，会调用 `discover_plugins` 方法扫描 `plugins_dir` 下的子目录。
- 每个服务类型 (如 `ASR`, `TTS`, `LLM`) 预计有其专属的子目录 (例如 `plugins/ASR/`, `plugins/LLM/`)。
- 在每个服务类型目录中，进一步扫描各个插件目录。
- 一个有效的插件目录必须包含 `plugin.json` 文件 (定义插件元数据，详见 [数据模型](./models.md) 中的 `PluginConfig` 和 `PluginInfo` 部分) 和 `pyproject.toml` 文件。
- 管理器会验证 `plugin.json` 中声明的 `service_type` 是否与插件所在的目录层级匹配。
- 发现的插件信息被解析并存储在 `plugins` 字典中，同时按服务类型归类到 `plugins_by_service` 字典。
- **Schema 加载**: 插件的 JSON Schema 和 UI Schema 直接从 `plugin.json` 中的 `plugin_json_schema` 和 `plugin_ui_schema` 字段加载，无需读取额外文件。

### 2. 环境同步 (`_sync_plugin_environment`)

- 在成功发现一个插件后，如果全局配置 `settings.enable_uv_sync` 为 `True`，管理器会调用 `_sync_plugin_environment` 方法。
- 此方法会在该插件的根目录 (`plugin_dir`)下执行 `uv sync` 命令，以安装或更新 `pyproject.toml` 中定义的依赖。
- 同步操作的超时时间由 `settings.uv_sync_timeout` 全局配置项控制。
- 如果 `uv` 命令不存在或同步过程失败/超时，会记录相应的警告或错误日志，但通常不会阻止插件管理器的其他操作。

### 3. 启动插件服务 (`start_plugin_service`)

当请求启动一个插件服务时，例如通过调用 `start_plugin_service(plugin_name)`：

- **有效性检查**: 首先检查插件是否存在以及是否已在运行。
- **端口分配**: 调用 `PortManager` 的 `allocate_port(plugin_name)` 方法为该插件实例分配一个唯一的可用端口。如果分配失败，将抛出 `ServiceError`。
- **命令构建**: 构造用于启动插件的命令。当前实现中，该命令固定为使用 `uv` 和 `uvicorn` 来运行插件内置的 FastAPI 应用：
  ```bash
  uv run --project <plugin_dir> uvicorn <plugin_name>.server:app --host 127.0.0.1 --port <allocated_port> --log-level info --access-log --no-use-colors
  ```
  这里 `<plugin_name>` 是插件目录的名称。此命令约定插件的根目录下有一个与插件同名的 Python 包（或模块），该包内含 `server.py` 文件，且此文件定义了一个名为 `app` 的 FastAPI 应用实例。
- **进程启动**: 使用 `subprocess.Popen` 在插件的 `plugin_dir` 目录下执行上述命令，以非阻塞方式启动插件进程。插件进程的 `stdout` 和 `stderr` 会被管道捕获。
- **日志捕获**: 为新启动的进程创建独立的线程 (`_read_plugin_logs`)，用于实时读取其 `stdout` 和 `stderr`，并将格式化后的日志行存储到 `plugin_logs` 字典中，并同时输出到主日志。
- **健康检查**: 启动进程后，会调用 `_wait_for_service_ready` 方法。该方法会异步轮询插件暴露的 `/health` HTTP 端点 (例如 `http://127.0.0.1:<allocated_port>/health`)。轮询会持续进行，直到收到 `200 OK` 响应，或达到 `settings.plugin_startup_timeout` 定义的超时时间。如果超时，会抛出 `ServiceError`，并尝试清理已分配的端口和进程记录。
- **状态更新**: 成功启动并健康检查通过后，插件的进程对象和端口号会存入 `running_services` 字典。

### 4. 停止插件服务 (`stop_plugin_service`)

当请求停止一个插件服务时，例如通过调用 `stop_plugin_service(plugin_name)`：

- **有效性检查**: 首先检查插件是否存在以及是否确实在运行。
- **进程终止**:
    1.  向插件进程发送 `terminate()`信号，尝试优雅关闭。
    2.  等待一段短时间 (10秒)。
    3.  如果进程仍然存在，则发送 `kill()`信号强制终止。
- **端口释放**: 调用 `PortManager` 的 `release_port(plugin_name)` 方法释放该插件占用的端口。
- **状态清理**: 从 `running_services` 中移除该插件的条目，并清理其在 `plugin_logs` 中的日志记录以及相关的日志读取线程。

### 5. 日志管理

- `_read_plugin_logs(plugin_name, process)`: 此内部方法为每个运行的插件启动两个守护线程，分别读取其 `stdout` 和 `stderr`。
- 日志行经过格式化（添加时间戳和插件名）后，会：
    1.  通过 Python `logging`模块记录到 Launcher 的主日志流中。
    2.  追加到 `plugin_logs[plugin_name]` 列表中（内存中最多保留最近1000条）。
- `get_plugin_logs(plugin_name, lines=100)`: 允许外部调用者获取指定插件最近的若干条日志。
- `clear_plugin_logs(plugin_name)`: 清空特定插件在内存中存储的日志。

### 6. Schema 管理

- **Schema 获取**: 通过 `get_plugin_schemas(plugin_name)` 方法从插件的 `plugin.json` 中直接获取 JSON Schema 和 UI Schema。
- **默认配置**: 插件可以在 `plugin_json_schema.default` 中定义默认配置值，用于创建新实例时的表单初始化。
- **Schema 验证**: 前端使用 JSON Schema 对用户输入进行验证，确保配置的正确性。

## 主要接口方法

下表总结了 `UnifiedPluginManager` 提供的一些主要公共方法及其功能：

| 方法名                             | 描述                                                                 |
|------------------------------------|----------------------------------------------------------------------|
| `discover_plugins()`               | （在初始化时自动调用）发现 `plugins_dir` 中的所有可用插件并加载 Schema。                                |
| `start_plugin_service(plugin_name)`| 异步启动指定名称的插件服务，并返回其运行端口。                                       |
| `stop_plugin_service(plugin_name)` | 停止指定名称的插件服务。                                                   |
| `stop_all_services()`              | 停止所有当前正在运行的插件服务。                                               |
| `get_plugins_by_service(service_type)`| 获取指定服务类型的所有插件的列表，包含它们的状态、版本等信息。                             |
| `get_all_plugins()`                | 获取所有已发现插件的列表（跨服务类型），包含它们的状态、版本等信息。                           |
| `get_plugin_schemas(plugin_name)`  | 获取指定插件的配置 Schema (包括 `plugin_json_schema`、`plugin_ui_schema`)，直接从 `plugin.json` 读取。 |
| `get_plugin_port(plugin_name)`     | 获取正在运行的指定插件所使用的端口号。如果插件未运行，则返回 `None`。                           |
| `is_plugin_running(plugin_name)`   | 检查指定的插件服务当前是否正在运行。                                             |
| `get_port_status()`                | 获取 `PortManager` 管理的当前端口分配状态。                                    |
| `configure_ports(port_ranges)`     | 重新配置 `PortManager` 使用的端口范围。此更改仅影响后续的端口分配。                             |
| `get_plugin_logs(plugin_name, lines)`| 获取指定插件最近存储的 `lines` 条日志。                                        |
| `clear_plugin_logs(plugin_name)`   | 清除为指定插件存储的日志记录。                                                 |
| `create_plugin_instance(plugin_name, config)` | 为指定插件创建新的配置实例。 |
| `list_plugin_instances(plugin_name)` | 列出指定插件的所有实例。 |
| `delete_plugin_instance(plugin_name, instance_id)` | 删除指定插件的特定实例。 |

## 依赖与配置

`UnifiedPluginManager` 的行为和功能依赖于 `LauncherSettings` 中定义的多项配置。这些配置通常通过环境变量或 `.env` 文件设置，详见 [配置管理](./configuration.md)。关键配置项包括：

- `OLV_LAUNCHER_PLUGINS_DIR`: 指定插件的根目录。
- `OLV_LAUNCHER_DEFAULT_PORT_RANGES`: 定义了插件服务可供分配的端口号范围列表。
- `OLV_LAUNCHER_PLUGIN_STARTUP_TIMEOUT`: 插件启动后，等待其健康检查通过的超时时间（秒）。
- `OLV_LAUNCHER_ENABLE_UV_SYNC`: 布尔值，控制是否在插件发现时自动运行 `uv sync`。
- `OLV_LAUNCHER_UV_SYNC_TIMEOUT`: `uv sync` 命令执行的超时时间（秒）。

## 插件开发指南

### 基本 plugin.json 结构

```json
{
  "name": "my_asr_plugin",
  "version": "1.0.0",
  "description": "My ASR Plugin",
  "author": "Developer Name",
  "service_type": "asr",
  "package_manager": "uv",
  "plugin_json_schema": {
    "type": "object",
    "title": "Plugin Configuration",
    "properties": {
      "model_path": {
        "type": "string",
        "title": "Model Path"
      },
      "sample_rate": {
        "type": "integer",
        "title": "Sample Rate",
        "default": 16000
      }
    },
    "required": ["model_path"],
    "default": {
      "model_path": "./models/default.onnx",
      "sample_rate": 16000
    }
  },
  "plugin_ui_schema": {
    "ui:title": "Plugin Configuration",
    "model_path": {
      "ui:widget": "textarea",
      "ui:placeholder": "Enter model path..."
    },
    "sample_rate": {
      "ui:widget": "updown"
    }
  }
}
```

### Schema 设计最佳实践

1. **提供默认值**: 在 `plugin_json_schema.default` 中提供合理的默认配置
2. **清晰的标题和描述**: 使用 `title` 和 `description` 字段提供用户友好的说明
3. **适当的验证**: 使用 `minimum`, `maximum`, `enum` 等约束确保配置有效性
4. **UI 优化**: 在 `plugin_ui_schema` 中选择合适的控件类型和布局