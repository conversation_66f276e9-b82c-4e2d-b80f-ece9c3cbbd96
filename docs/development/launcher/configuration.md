# 配置管理 (Configuration)

OLV Launcher 使用 Pydantic 的 `BaseSettings` 来管理配置。这意味着配置项可以从环境变量或 `.env` 文件中加载。所有环境变量都应以 `OLV_LAUNCHER_` 作为前缀。

## 配置加载

配置项按以下优先级加载：

1.  环境变量 (例如 `OLV_LAUNCHER_HOST`)
2.  项目根目录下的 `.env` 文件中定义的变量 (其格式与环境变量相同，例如 `OLV_LAUNCHER_HOST="0.0.0.0"`)
3.  代码中定义的默认值

## 配置项

下表列出了 OLV Launcher 的所有可用配置项。这些变量名同时适用于操作系统的环境变量和 `.env` 文件。

| `.env` 文件变量名 / 环境变量名          | 描述                                   | 类型        | 默认值                                      |
| --------------------------------------- | -------------------------------------- | ----------- | ------------------------------------------- |
| `OLV_LAUNCHER_HOST`                     | 服务器监听的主机地址。                         | `str`       | `"127.0.0.1"`                               |
| `OLV_LAUNCHER_PORT`                     | 服务器监听的端口号。                           | `int`       | `7000` (有效范围: 1024-65535)               |
| `OLV_LAUNCHER_RELOAD`                   | 是否在开发模式下启用自动重新加载。                 | `bool`      | `False`                                     |
| `OLV_LAUNCHER_PLUGINS_DIR`              | 存放插件的目录。                             | `str`       | `"plugins"`                                 |
| `OLV_LAUNCHER_DEFAULT_PORT_RANGES`      | 插件分配的默认端口范围。这是一个字符串列表，每个字符串格式为 "start-end"。 | `List[str]` | `["8001-8020", "9001-9020", "10001-10020"]` |
| `OLV_LAUNCHER_HEALTH_CHECK_INTERVAL`    | 健康检查的时间间隔（秒）。                         | `int`       | `30` (最小: 5)                            |
| `OLV_LAUNCHER_PLUGIN_STARTUP_TIMEOUT`   | 插件启动的超时时间（秒）。                       | `int`       | `600` (最小: 30)                           |
| `OLV_LAUNCHER_LOG_LEVEL`                | 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)。 | `str`       | `"INFO"`                                    |
| `OLV_LAUNCHER_LOG_FORMAT`               | 日志格式字符串。                             | `str`       | `"%(asctime)s - %(name)s - %(levelname)s - %(message)s"` |
| `OLV_LAUNCHER_CORS_ORIGINS`             | 允许的 CORS 来源列表。                        | `List[str]` | `["*"]`                                     |
| `OLV_LAUNCHER_CORS_CREDENTIALS`         | 是否在 CORS 请求中允许凭据。                  | `bool`      | `True`                                      |
| `OLV_LAUNCHER_CORS_METHODS`             | 允许的 CORS 方法列表。                        | `List[str]` | `["*"]`                                     |
| `OLV_LAUNCHER_CORS_HEADERS`             | 允许的 CORS 头部列表。                        | `List[str]` | `["*"]`                                     |
| `OLV_LAUNCHER_ENABLE_UV_SYNC`           | 是否启用自动 UV 环境同步。                     | `bool`      | `True`                                      |
| `OLV_LAUNCHER_UV_SYNC_TIMEOUT`          | UV 环境同步的超时时间（秒）。                    | `int`       | `300` (最小: 60)                           |
