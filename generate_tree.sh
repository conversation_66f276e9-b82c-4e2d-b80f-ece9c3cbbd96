#!/bin/bash

echo '```plaintext'

for dir in src plugins; do
  if [ -d "$dir" ]; then
    echo "$dir/"
    find "$dir" -type d \( -name '__pycache__' -prune \) -o -type f -name '*.py' | \
    grep -v '__pycache__' | \
    sed "s|^$dir/|  |" | \
    sed 's|/|  |g'
  fi
done

if [ -d docs ]; then
  echo "docs/"
  find docs -type f -name '*.md' | sed 's|^docs/|  |'
fi

if [ -d olv-launcher-ui ]; then
  echo "olv-launcher-ui/"
  find olv-launcher-ui \
    -path 'olv-launcher-ui/node_modules' -prune -o \
    -path 'olv-launcher-ui/.next' -prune -o \
    -path 'olv-launcher-ui/.git' -prune -o \
    -path 'olv-launcher-ui/.vercel' -prune -o \
    -type f -o -type d | \
    grep -v -E 'node_modules|.next|.git|.vercel' | \
    sed 's|^olv-launcher-ui/|  |'
fi

echo '```'